# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env

# Database
*.db
*.sqlite
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# Data
data/
processed_invoices/

# Don't ignore files in the "files" folder (invoice input folder)
!files/
!files/*.pdf
!files/*.png
!files/*.jpg
!files/*.jpeg

# Logs
*.log


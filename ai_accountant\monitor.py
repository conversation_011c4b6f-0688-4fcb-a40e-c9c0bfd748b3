"""File monitoring service for automatic invoice processing"""

import time
import shutil
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ai_accountant.config import Config
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator


class InvoiceHandler(FileSystemEventHandler):
    """Handle new invoice files"""
    
    def __init__(self):
        self.parser = InvoiceParser()
        self.generator = JournalEntryGenerator()
        self.processing = set()  # Track files being processed
    
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        # Check if file extension is supported
        if file_path.suffix.lower() not in Config.SUPPORTED_EXTENSIONS:
            return
        
        # Avoid processing the same file multiple times
        if str(file_path) in self.processing:
            return
        
        print(f"\n📄 New invoice detected: {file_path.name}")
        
        # Wait a bit to ensure file is fully written
        time.sleep(2)
        
        try:
            self.processing.add(str(file_path))
            self._process_invoice(file_path)
        except Exception as e:
            print(f"❌ Error processing {file_path.name}: {e}")
        finally:
            self.processing.discard(str(file_path))
    
    def _process_invoice(self, file_path: Path):
        """Process a single invoice file"""
        try:
            # Parse invoice
            print(f"🔍 Parsing invoice...")
            invoice_data = self.parser.parse_invoice(str(file_path))
            
            vendor = invoice_data.get("vendor_name", "Unknown")
            total = invoice_data.get("total_amount", 0)
            currency = invoice_data.get("currency", "USD")
            
            print(f"✅ Parsed: {vendor} - {currency} {total}")
            
            # Generate journal entry
            print(f"📝 Generating journal entry...")
            journal_entry = self.generator.process_invoice(invoice_data)
            
            print(f"✅ Journal entry #{journal_entry.id} created (Status: {journal_entry.status})")
            print(f"   Use 'python -m ai_accountant.cli review' to review pending entries")
            
            # Move processed file
            processed_path = Config.PROCESSED_DIR / file_path.name
            shutil.move(str(file_path), str(processed_path))
            print(f"📦 Moved to: {processed_path}")
            
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            raise


class InvoiceMonitor:
    """Monitor directory for new invoices"""
    
    def __init__(self, watch_path: str = None):
        self.watch_path = watch_path or Config.DESKTOP_PATH
        self.observer = Observer()
        self.handler = InvoiceHandler()
    
    def start(self):
        """Start monitoring"""
        watch_path = Path(self.watch_path)
        
        if not watch_path.exists():
            print(f"Creating watch directory: {watch_path}")
            watch_path.mkdir(parents=True, exist_ok=True)
        
        print(f"👀 Monitoring: {watch_path}")
        print(f"📊 Supported formats: {', '.join(Config.SUPPORTED_EXTENSIONS)}")
        print(f"🤖 AI Provider: {Config.AI_PROVIDER}")
        print(f"\nWaiting for invoices... (Press Ctrl+C to stop)\n")
        
        self.observer.schedule(self.handler, str(watch_path), recursive=False)
        self.observer.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Stopping monitor...")
            self.observer.stop()
        
        self.observer.join()
        print("✅ Monitor stopped")


def main():
    """Main entry point for monitor"""
    try:
        Config.validate()
        monitor = InvoiceMonitor()
        monitor.start()
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())


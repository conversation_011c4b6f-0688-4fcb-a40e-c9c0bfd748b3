# Changelog

## Version 0.1.0 - Initial Release (2025-10-02)

### Features
- ✅ AI-powered invoice parsing (OpenAI GPT-4 / Anthropic Claude)
- ✅ OCR support for scanned documents (Tesseract)
- ✅ Rule-based accounting (vendor, category, keyword, default rules)
- ✅ Double-entry journal entry generation
- ✅ Automatic file monitoring
- ✅ Interactive CLI for review and approval
- ✅ SQLite database with full audit trail
- ✅ JSON export of parsed invoices
- ✅ Sample chart of accounts
- ✅ Comprehensive documentation

### Configuration
- **Invoice Input Folder**: `./files/` (relative to project root)
- **Database**: `./data/accountant.db`
- **Parsed JSON**: `./data/parsed_json/`
- **Processed Files**: `./data/processed_invoices/`

### Usage
```bash
# Start monitoring
python -m ai_accountant.monitor

# Drop invoices in ./files folder
# System processes automatically

# Review and approve
python -m ai_accountant.cli review
```

### Documentation
- START_HERE.md - Quick start guide
- GETTING_STARTED.md - Setup instructions
- PROJECT_SUMMARY.md - Feature overview
- SETUP_GUIDE.md - Detailed installation
- ARCHITECTURE.md - System design
- EXAMPLES.md - Usage examples
- PROJECT_STRUCTURE.txt - File structure

### Technical Stack
- Python 3.8+
- SQLAlchemy + SQLite
- OpenAI / Anthropic APIs
- Tesseract OCR
- Watchdog (file monitoring)
- Typer + Rich (CLI)

### Known Limitations
- Single-user system (no multi-user support yet)
- Local processing only (no cloud deployment)
- Manual approval required (no auto-posting to accounting software)
- English language only

### Future Enhancements
- QuickBooks/Xero integration
- Multi-currency support
- Web interface
- Machine learning from approvals
- Email invoice processing
- Multi-user support


"""Database initialization script"""

from ai_accountant.models import init_database, get_session, ChartOfAccounts, AccountingRule
from ai_accountant.rules_engine import RulesEngine


def setup_sample_chart_of_accounts():
    """Create sample chart of accounts"""
    session = get_session()
    
    accounts = [
        # Assets
        ("1000", "Cash", "Asset", None, "Cash and cash equivalents"),
        ("1100", "Accounts Receivable", "Asset", None, "Money owed by customers"),
        ("1200", "Inventory", "Asset", None, "Goods for sale"),
        ("1500", "Equipment", "Asset", None, "Office equipment and furniture"),
        
        # Liabilities
        ("2000", "Accounts Payable", "Liability", None, "Money owed to vendors"),
        ("2100", "Credit Card Payable", "Liability", None, "Credit card balances"),
        ("2200", "Accrued Expenses", "Liability", None, "Expenses incurred but not yet paid"),
        
        # Equity
        ("3000", "Owner's Equity", "Equity", None, "Owner's investment in business"),
        ("3100", "Retained Earnings", "Equity", None, "Accumulated profits"),
        
        # Revenue
        ("4000", "Sales Revenue", "Revenue", None, "Revenue from sales"),
        ("4100", "Service Revenue", "Revenue", None, "Revenue from services"),
        
        # Expenses
        ("6000", "General Expenses", "Expense", None, "General operating expenses"),
        ("6100", "Office Supplies", "Expense", "6000", "Office supplies and materials"),
        ("6200", "Software & Subscriptions", "Expense", "6000", "Software licenses and subscriptions"),
        ("6300", "Utilities", "Expense", "6000", "Electricity, water, internet"),
        ("6400", "Rent", "Expense", "6000", "Office rent"),
        ("6500", "Professional Services", "Expense", "6000", "Legal, accounting, consulting"),
        ("6600", "Marketing & Advertising", "Expense", "6000", "Marketing and advertising costs"),
        ("6700", "Travel & Entertainment", "Expense", "6000", "Business travel and meals"),
        ("6800", "Insurance", "Expense", "6000", "Business insurance"),
        ("6900", "Depreciation", "Expense", "6000", "Asset depreciation"),
    ]
    
    for code, name, acc_type, parent, desc in accounts:
        # Check if account already exists
        existing = session.query(ChartOfAccounts).filter_by(account_code=code).first()
        if not existing:
            account = ChartOfAccounts(
                account_code=code,
                account_name=name,
                account_type=acc_type,
                parent_account=parent,
                description=desc
            )
            session.add(account)
    
    session.commit()
    print("✓ Sample chart of accounts created")


def setup_sample_rules():
    """Create sample accounting rules"""
    engine = RulesEngine()
    
    # Check if rules already exist
    existing_rules = engine.list_rules()
    if existing_rules:
        print("✓ Accounting rules already exist")
        return
    
    # Add sample vendor rules
    engine.add_vendor_rule(
        "Amazon",
        "6100",
        "Office Supplies",
        "Amazon purchases default to office supplies"
    )
    
    engine.add_vendor_rule(
        "Microsoft",
        "6200",
        "Software & Subscriptions",
        "Microsoft software and services"
    )
    
    engine.add_vendor_rule(
        "Adobe",
        "6200",
        "Software & Subscriptions",
        "Adobe Creative Cloud subscriptions"
    )
    
    # Add sample category rules
    engine.add_category_rule(
        "Software",
        "6200",
        "Software & Subscriptions",
        "Software-related expenses"
    )
    
    engine.add_category_rule(
        "Office",
        "6100",
        "Office Supplies",
        "Office supplies and materials"
    )
    
    engine.add_category_rule(
        "Marketing",
        "6600",
        "Marketing & Advertising",
        "Marketing and advertising expenses"
    )
    
    # Add default rule
    engine.add_default_rule()
    
    print("✓ Sample accounting rules created")


def main():
    """Initialize database with tables and sample data"""
    print("\n🔧 Initializing AI Accountant Database...\n")
    
    try:
        # Create tables
        init_database()
        
        # Add sample data
        setup_sample_chart_of_accounts()
        setup_sample_rules()
        
        print("\n✅ Database initialization complete!\n")
        print("Next steps:")
        print("  1. Copy .env.example to .env and configure your API keys")
        print("  2. Run 'python -m ai_accountant.monitor' to start monitoring")
        print("  3. Or use 'python -m ai_accountant.cli process <file>' to process a single invoice\n")
        
    except Exception as e:
        print(f"\n❌ Error during initialization: {e}\n")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())


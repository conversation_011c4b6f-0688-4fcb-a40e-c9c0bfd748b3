AI ACCOUNTANT - PROJECT STRUCTURE
==================================

📁 ai_accountant/                          # Root directory
│
├── 📖 DOCUMENTATION (Start Here!)
│   ├── START_HERE.md                      # 👈 READ THIS FIRST!
│   ├── GETTING_STARTED.md                 # Quick setup guide (5-15 min)
│   ├── PROJECT_SUMMARY.md                 # Feature overview & capabilities
│   ├── SETUP_GUIDE.md                     # Detailed installation guide
│   ├── ARCHITECTURE.md                    # System design & internals
│   ├── EXAMPLES.md                        # Code examples & use cases
│   ├── README.md                          # Project overview
│   └── PROJECT_STRUCTURE.txt              # This file
│
├── 🐍 PYTHON PACKAGE
│   └── ai_accountant/                     # Main package
│       ├── __init__.py                    # Package initialization
│       ├── config.py                      # Configuration management
│       │                                  # - Environment variables
│       │                                  # - Path management
│       │                                  # - API key validation
│       │
│       ├── models.py                      # Database models (SQLAlchemy)
│       │                                  # - Invoice
│       │                                  # - InvoiceLineItem
│       │                                  # - AccountingRule
│       │                                  # - JournalEntry
│       │                                  # - JournalEntryLine
│       │                                  # - ChartOfAccounts
│       │
│       ├── parser.py                      # Invoice parsing
│       │                                  # - PDF text extraction
│       │                                  # - OCR for images
│       │                                  # - AI parsing (OpenAI/Anthropic)
│       │                                  # - JSON export
│       │
│       ├── rules_engine.py                # Rule matching logic
│       │                                  # - Vendor rules
│       │                                  # - Category rules
│       │                                  # - Keyword rules
│       │                                  # - Default fallback
│       │
│       ├── journal_entry.py               # Journal entry generation
│       │                                  # - Double-entry bookkeeping
│       │                                  # - Debit/credit lines
│       │                                  # - Entry validation
│       │                                  # - Approval workflow
│       │
│       ├── monitor.py                     # File monitoring service
│       │                                  # - Watchdog integration
│       │                                  # - Automatic processing
│       │                                  # - Error handling
│       │
│       ├── cli.py                         # Command-line interface
│       │                                  # - Process command
│       │                                  # - Review command
│       │                                  # - Rules management
│       │                                  # - Rich terminal UI
│       │
│       └── init_db.py                     # Database initialization
│                                          # - Create tables
│                                          # - Sample chart of accounts
│                                          # - Default rules
│
├── 🧪 TESTS
│   └── tests/
│       ├── __init__.py
│       └── test_rules_engine.py           # Unit tests for rules engine
│
├── 📂 INPUT FOLDER
│   └── files/                             # Drop invoices here!
│       ├── README.md                      # Instructions for this folder
│       └── (your invoice files)           # PDF, PNG, JPG, etc.
│
├── 📊 DATA (Created on first run)
│   └── data/
│       ├── accountant.db                  # SQLite database
│       ├── parsed_json/                   # Parsed invoice JSON files
│       │   └── invoice_name_parsed.json
│       └── processed_invoices/            # Moved invoice files
│           └── invoice_name.pdf
│
├── ⚙️ CONFIGURATION
│   ├── .env.example                       # Example environment config
│   ├── .env                               # Your config (CREATE THIS!)
│   │                                      # - AI_PROVIDER=openai
│   │                                      # - OPENAI_API_KEY=sk-...
│   │                                      # - DESKTOP_PATH=./files
│   │
│   ├── requirements.txt                   # Python dependencies
│   │                                      # - openai / anthropic
│   │                                      # - pytesseract
│   │                                      # - sqlalchemy
│   │                                      # - watchdog
│   │                                      # - typer, rich
│   │
│   └── .gitignore                         # Git ignore rules
│
├── 🚀 UTILITIES
│   ├── quickstart.py                      # System check script
│   │                                      # - Verify installation
│   │                                      # - Check dependencies
│   │                                      # - Validate config
│   │
│   └── sample_invoice.txt                 # Test invoice
│                                          # - Use for testing
│                                          # - Example format
│
└── 🔧 VIRTUAL ENVIRONMENT (You create this)
    └── venv/                              # Python virtual environment
        ├── Scripts/                       # Windows
        │   └── activate                   # Activation script
        └── bin/                           # Linux/Mac
            └── activate                   # Activation script


QUICK REFERENCE
===============

📖 Documentation Reading Order:
   1. START_HERE.md          - Overview & quick start
   2. GETTING_STARTED.md     - Setup instructions
   3. PROJECT_SUMMARY.md     - Features & capabilities
   4. EXAMPLES.md            - Usage examples
   5. ARCHITECTURE.md        - System design

🚀 Essential Commands:
   Setup:
   $ python -m venv venv
   $ venv\Scripts\activate
   $ pip install -r requirements.txt
   $ python -m ai_accountant.init_db

   Process Invoice:
   $ python -m ai_accountant.cli process invoice.pdf

   Start Monitor:
   $ python -m ai_accountant.monitor

   Review Entries:
   $ python -m ai_accountant.cli review

   Manage Rules:
   $ python -m ai_accountant.cli rules list
   $ python -m ai_accountant.cli add-vendor-rule "Vendor" --account "6100" --name "Office Supplies"

   System Check:
   $ python quickstart.py

📁 Important Paths:
   Database:        data/accountant.db
   Parsed JSON:     data/parsed_json/
   Processed Files: data/processed_invoices/
   Config:          .env
   Logs:            Console output

🔑 Configuration (.env):
   AI_PROVIDER=openai                      # or "anthropic"
   OPENAI_API_KEY=sk-your-key-here
   DESKTOP_PATH=./files                    # Invoice input folder
   DATABASE_PATH=./data/accountant.db

📊 Database Tables:
   - invoices                 # Parsed invoice data
   - invoice_line_items       # Line items
   - accounting_rules         # Rule definitions
   - journal_entries          # Generated entries
   - journal_entry_lines      # Debit/credit lines
   - chart_of_accounts        # Account definitions

🎯 Workflow:
   1. Drop invoice in ./files folder
   2. System detects and parses
   3. AI extracts structured data
   4. Rules engine matches accounts
   5. Journal entry generated
   6. You review and approve
   7. Entry saved to database

💡 Tips:
   - Start with sample_invoice.txt
   - Run quickstart.py to verify setup
   - Add rules as you process invoices
   - Review entries regularly
   - Back up data/accountant.db
   - Keep .env file secure

🆘 Troubleshooting:
   - Check SETUP_GUIDE.md troubleshooting section
   - Run quickstart.py for system check
   - Verify .env configuration
   - Check console output for errors
   - Test with sample_invoice.txt first

📞 Getting Help:
   1. Read START_HERE.md
   2. Check GETTING_STARTED.md
   3. Review EXAMPLES.md
   4. Run quickstart.py
   5. Check error messages


FILE SIZES (Approximate)
========================
Documentation:     ~50 KB total
Python Code:       ~30 KB total
Database:          ~100 KB (empty), grows with data
Dependencies:      ~200 MB (in venv)


SYSTEM REQUIREMENTS
===================
- Python 3.8 or higher
- 500 MB disk space (for dependencies)
- Internet connection (for AI API calls)
- OpenAI or Anthropic API key
- Optional: Tesseract OCR (for images)


SUPPORTED FILE FORMATS
======================
Input:  PDF, PNG, JPG, JPEG, TIFF, BMP
Output: JSON, SQLite database


COST ESTIMATE
=============
API Costs:
- OpenAI GPT-4:     ~$0.01-0.03 per invoice
- Anthropic Claude: ~$0.01-0.02 per invoice

Example: 100 invoices/month = $1-3/month


NEXT STEPS
==========
1. Read START_HERE.md
2. Run: python quickstart.py
3. Process: python -m ai_accountant.cli process sample_invoice.txt
4. Review: python -m ai_accountant.cli review
5. Customize rules for your business
6. Set up automatic monitoring


VERSION INFORMATION
===================
Version:     0.1.0
Status:      Production Ready ✅
License:     MIT
Python:      3.8+
Last Update: 2025-10-02


CONTACT & SUPPORT
=================
Documentation: See all .md files
Issues:        Check SETUP_GUIDE.md troubleshooting
Testing:       Use sample_invoice.txt
Customization: Modify rules via CLI


═══════════════════════════════════════════════════════════════
  AI ACCOUNTANT - Intelligent Invoice Processing & Automation
═══════════════════════════════════════════════════════════════


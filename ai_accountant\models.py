"""Database models for AI Accountant"""

from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from ai_accountant.config import Config

Base = declarative_base()


class Invoice(Base):
    """Parsed invoice data"""
    __tablename__ = "invoices"
    
    id = Column(Integer, primary_key=True)
    file_path = Column(String(500), nullable=False)
    file_name = Column(String(255), nullable=False)
    processed_at = Column(DateTime, default=datetime.utcnow)
    
    # Parsed data
    vendor_name = Column(String(255))
    invoice_number = Column(String(100))
    invoice_date = Column(String(50))
    due_date = Column(String(50))
    subtotal = Column(Float)
    tax_amount = Column(Float)
    total_amount = Column(Float)
    currency = Column(String(10), default="USD")
    
    # Raw parsed JSON
    raw_json = Column(Text)
    
    # Status
    status = Column(String(50), default="parsed")  # parsed, processed, approved, rejected
    
    # Relationships
    line_items = relationship("InvoiceLineItem", back_populates="invoice", cascade="all, delete-orphan")
    journal_entries = relationship("JournalEntry", back_populates="invoice", cascade="all, delete-orphan")


class InvoiceLineItem(Base):
    """Individual line items from invoices"""
    __tablename__ = "invoice_line_items"
    
    id = Column(Integer, primary_key=True)
    invoice_id = Column(Integer, ForeignKey("invoices.id"), nullable=False)
    
    description = Column(Text)
    quantity = Column(Float)
    unit_price = Column(Float)
    amount = Column(Float)
    category = Column(String(100))
    
    invoice = relationship("Invoice", back_populates="line_items")


class AccountingRule(Base):
    """Rules for mapping invoices to accounts"""
    __tablename__ = "accounting_rules"
    
    id = Column(Integer, primary_key=True)
    rule_type = Column(String(50), nullable=False)  # vendor, category, keyword, default
    
    # Matching criteria
    match_value = Column(String(255))  # vendor name, category, keyword
    match_pattern = Column(String(255))  # regex pattern for advanced matching
    
    # Account mapping
    debit_account = Column(String(50))
    debit_account_name = Column(String(255))
    credit_account = Column(String(50))
    credit_account_name = Column(String(255))
    
    # Additional info
    description = Column(Text)
    priority = Column(Integer, default=0)  # Higher priority rules are checked first
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class JournalEntry(Base):
    """Generated journal entries"""
    __tablename__ = "journal_entries"
    
    id = Column(Integer, primary_key=True)
    invoice_id = Column(Integer, ForeignKey("invoices.id"), nullable=False)
    
    entry_date = Column(DateTime, default=datetime.utcnow)
    description = Column(Text)
    reference = Column(String(100))  # Invoice number or reference
    
    # Status
    status = Column(String(50), default="proposed")  # proposed, approved, rejected, posted
    approved_at = Column(DateTime)
    approved_by = Column(String(100))
    
    # Relationships
    invoice = relationship("Invoice", back_populates="journal_entries")
    lines = relationship("JournalEntryLine", back_populates="journal_entry", cascade="all, delete-orphan")


class JournalEntryLine(Base):
    """Individual debit/credit lines in journal entries"""
    __tablename__ = "journal_entry_lines"
    
    id = Column(Integer, primary_key=True)
    journal_entry_id = Column(Integer, ForeignKey("journal_entries.id"), nullable=False)
    
    account_code = Column(String(50), nullable=False)
    account_name = Column(String(255), nullable=False)
    description = Column(Text)
    debit = Column(Float, default=0.0)
    credit = Column(Float, default=0.0)
    
    journal_entry = relationship("JournalEntry", back_populates="lines")


class ChartOfAccounts(Base):
    """Chart of accounts for reference"""
    __tablename__ = "chart_of_accounts"
    
    id = Column(Integer, primary_key=True)
    account_code = Column(String(50), unique=True, nullable=False)
    account_name = Column(String(255), nullable=False)
    account_type = Column(String(50), nullable=False)  # Asset, Liability, Equity, Revenue, Expense
    parent_account = Column(String(50))
    description = Column(Text)
    is_active = Column(Boolean, default=True)


# Database setup
def get_engine():
    """Get database engine"""
    return create_engine(f"sqlite:///{Config.DATABASE_PATH}")


def get_session():
    """Get database session"""
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    return Session()


def init_database():
    """Initialize database with tables"""
    engine = get_engine()
    Base.metadata.create_all(engine)
    print(f"Database initialized at {Config.DATABASE_PATH}")


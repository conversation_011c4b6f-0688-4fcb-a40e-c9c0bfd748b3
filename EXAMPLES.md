# AI Accountant - Usage Examples

## Example 1: Processing an AWS Invoice

### Input Invoice (sample_invoice.txt)
```
INVOICE

Amazon Web Services, Inc.
Invoice Number: INV-2024-001
Invoice Date: January 15, 2024
Total: $68.19
```

### Command
```bash
python -m ai_accountant.cli process sample_invoice.txt
```

### Output
```
Processing invoice: sample_invoice.txt

✓ Parsed: Amazon Web Services - USD 68.19
✓ Journal entry #1 created

╭─────────────────────────────────────────╮
│ Journal Entry #1                        │
│ Date: 2024-01-15                        │
│ Reference: INV-2024-001                 │
│ Status: proposed                        │
│                                         │
│ Vendor: Amazon Web Services             │
│ Invoice: INV-2024-001                   │
│ Total: USD 68.19                        │
╰─────────────────────────────────────────╯

Account                          Description              Debit    Credit
─────────────────────────────────────────────────────────────────────────
6100 - Office Supplies          Amazon - INV-2024-001    68.19
2000 - Accounts Payable         Amazon - INV-2024-001             68.19
TOTAL                                                    68.19    68.19

✓ Entry is balanced
```

### Parsed JSON (data/parsed_json/sample_invoice_parsed.json)
```json
{
  "vendor_name": "Amazon Web Services, Inc.",
  "invoice_number": "INV-2024-001",
  "invoice_date": "2024-01-15",
  "due_date": "2024-02-15",
  "subtotal": 61.99,
  "tax_amount": 6.20,
  "total_amount": 68.19,
  "currency": "USD",
  "line_items": [
    {
      "description": "AWS EC2 Instance (t3.medium)",
      "quantity": 730,
      "unit_price": 0.0416,
      "amount": 30.37,
      "category": "Software"
    },
    {
      "description": "AWS S3 Storage (100 GB)",
      "quantity": 100,
      "unit_price": 0.023,
      "amount": 2.30,
      "category": "Software"
    }
  ]
}
```

## Example 2: Adding Custom Rules

### Add a Vendor Rule for Microsoft
```bash
python -m ai_accountant.cli add-vendor-rule "Microsoft" \
  --account "6200" \
  --name "Software & Subscriptions" \
  --desc "Microsoft 365 and Azure services"
```

**Output:**
```
✓ Vendor rule created (ID: 5)
```

### Add a Category Rule for Marketing
```bash
python -m ai_accountant.cli add-category-rule "Marketing" \
  --account "6600" \
  --name "Marketing & Advertising" \
  --desc "Marketing and advertising expenses"
```

**Output:**
```
✓ Category rule created (ID: 6)
```

### List All Rules
```bash
python -m ai_accountant.cli rules list
```

**Output:**
```
ID  Type      Match        Debit Account                    Credit Account              Priority
────────────────────────────────────────────────────────────────────────────────────────────────
1   vendor    Amazon       6100 - Office Supplies          2000 - Accounts Payable     10
2   vendor    Microsoft    6200 - Software & Subscriptions 2000 - Accounts Payable     10
3   vendor    Adobe        6200 - Software & Subscriptions 2000 - Accounts Payable     10
4   category  Software     6200 - Software & Subscriptions 2000 - Accounts Payable     5
5   category  Office       6100 - Office Supplies          2000 - Accounts Payable     5
6   category  Marketing    6600 - Marketing & Advertising  2000 - Accounts Payable     5
7   default   *            6000 - General Expenses         2000 - Accounts Payable     0
```

## Example 3: Automatic Monitoring

### Start the Monitor
```bash
python -m ai_accountant.monitor
```

**Output:**
```
👀 Monitoring: C:\Users\<USER>\Desktop\invoices
📊 Supported formats: .pdf, .png, .jpg, .jpeg, .tiff, .bmp
🤖 AI Provider: openai

Waiting for invoices... (Press Ctrl+C to stop)
```

### Drop an Invoice File

When you drop `microsoft_invoice.pdf` into the folder:

```
📄 New invoice detected: microsoft_invoice.pdf
🔍 Parsing invoice...
✅ Parsed: Microsoft Corporation - USD 299.00
📝 Generating journal entry...
✅ Journal entry #2 created (Status: proposed)
   Use 'python -m ai_accountant.cli review' to review pending entries
📦 Moved to: data/processed_invoices/microsoft_invoice.pdf
```

## Example 4: Reviewing Pending Entries

### Review Command
```bash
python -m ai_accountant.cli review
```

**Output:**
```
Found 2 pending journal entries

╭─────────────────────────────────────────╮
│ Journal Entry #1                        │
│ Date: 2024-01-15T10:30:00               │
│ Reference: INV-2024-001                 │
│ Status: proposed                        │
│                                         │
│ Vendor: Amazon Web Services             │
│ Invoice: INV-2024-001                   │
│ Total: USD 68.19                        │
╰─────────────────────────────────────────╯

Account                          Description              Debit    Credit
─────────────────────────────────────────────────────────────────────────
6100 - Office Supplies          Amazon - INV-2024-001    68.19
2000 - Accounts Payable         Amazon - INV-2024-001             68.19
TOTAL                                                    68.19    68.19

✓ Entry is balanced

Action? [a]pprove / [r]eject / [s]kip / [q]uit: a
✓ Approved

╭─────────────────────────────────────────╮
│ Journal Entry #2                        │
│ Date: 2024-01-15T11:45:00               │
│ Reference: INV-2024-002                 │
│ Status: proposed                        │
│                                         │
│ Vendor: Microsoft Corporation           │
│ Invoice: INV-2024-002                   │
│ Total: USD 299.00                       │
╰─────────────────────────────────────────╯

Account                          Description                  Debit    Credit
─────────────────────────────────────────────────────────────────────────────
6200 - Software & Subscriptions Microsoft - INV-2024-002    299.00
2000 - Accounts Payable         Microsoft - INV-2024-002             299.00
TOTAL                                                       299.00   299.00

✓ Entry is balanced

Action? [a]pprove / [r]eject / [s]kip / [q]uit: a
✓ Approved

Review complete
```

## Example 5: Python API Usage

### Process Invoice Programmatically

```python
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator

# Parse invoice
parser = InvoiceParser()
invoice_data = parser.parse_invoice("path/to/invoice.pdf")

print(f"Vendor: {invoice_data['vendor_name']}")
print(f"Total: {invoice_data['total_amount']}")

# Generate journal entry
generator = JournalEntryGenerator()
journal_entry = generator.process_invoice(invoice_data)

print(f"Journal Entry ID: {journal_entry.id}")
print(f"Status: {journal_entry.status}")

# Approve entry
generator.approve_journal_entry(journal_entry.id, approved_by="admin")
```

### Add Rules Programmatically

```python
from ai_accountant.rules_engine import RulesEngine

engine = RulesEngine()

# Add vendor rule
rule = engine.add_vendor_rule(
    vendor_name="Salesforce",
    expense_account="6200",
    expense_account_name="Software & Subscriptions",
    description="Salesforce CRM subscription",
    priority=10
)

print(f"Rule created: {rule.id}")

# Find matching rule
invoice_data = {
    "vendor_name": "Salesforce.com",
    "line_items": []
}

matching_rule = engine.find_matching_rule(invoice_data)
if matching_rule:
    print(f"Matched rule: {matching_rule.description}")
```

### Query Database

```python
from ai_accountant.models import get_session, Invoice, JournalEntry

session = get_session()

# Get all approved invoices
approved_invoices = session.query(Invoice).filter(
    Invoice.status == "approved"
).all()

for invoice in approved_invoices:
    print(f"{invoice.vendor_name}: ${invoice.total_amount}")

# Get all journal entries for a specific vendor
entries = session.query(JournalEntry).join(Invoice).filter(
    Invoice.vendor_name.like("%Amazon%")
).all()

for entry in entries:
    print(f"Entry #{entry.id}: {entry.description}")
```

## Example 6: Batch Processing

### Process Multiple Invoices

```python
from pathlib import Path
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator

parser = InvoiceParser()
generator = JournalEntryGenerator()

invoice_dir = Path("invoices_to_process")

for invoice_file in invoice_dir.glob("*.pdf"):
    try:
        print(f"Processing {invoice_file.name}...")
        
        # Parse and process
        invoice_data = parser.parse_invoice(str(invoice_file))
        journal_entry = generator.process_invoice(invoice_data)
        
        print(f"  ✓ Created entry #{journal_entry.id}")
        
    except Exception as e:
        print(f"  ✗ Error: {e}")

print("Batch processing complete!")
```

## Example 7: Custom Workflow

### Auto-Approve Small Amounts

```python
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator

parser = InvoiceParser()
generator = JournalEntryGenerator()

# Process invoice
invoice_data = parser.parse_invoice("invoice.pdf")
journal_entry = generator.process_invoice(invoice_data)

# Auto-approve if under $100
if invoice_data.get("total_amount", 0) < 100:
    generator.approve_journal_entry(journal_entry.id, approved_by="auto")
    print(f"✓ Auto-approved (amount: ${invoice_data['total_amount']})")
else:
    print(f"⚠ Requires manual review (amount: ${invoice_data['total_amount']})")
```

## Example 8: Export to CSV

### Export Journal Entries

```python
import csv
from ai_accountant.models import get_session, JournalEntry, JournalEntryLine

session = get_session()

# Get all approved entries
entries = session.query(JournalEntry).filter(
    JournalEntry.status == "approved"
).all()

# Export to CSV
with open("journal_entries.csv", "w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow(["Date", "Reference", "Account", "Description", "Debit", "Credit"])
    
    for entry in entries:
        for line in entry.lines:
            writer.writerow([
                entry.entry_date.strftime("%Y-%m-%d"),
                entry.reference,
                f"{line.account_code} - {line.account_name}",
                line.description,
                line.debit if line.debit > 0 else "",
                line.credit if line.credit > 0 else ""
            ])

print("✓ Exported to journal_entries.csv")
```

## Example 9: Error Handling

### Robust Processing

```python
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

parser = InvoiceParser()
generator = JournalEntryGenerator()

def process_invoice_safe(file_path):
    """Process invoice with error handling"""
    try:
        # Parse
        logger.info(f"Parsing {file_path}")
        invoice_data = parser.parse_invoice(file_path)
        
        # Validate
        if not invoice_data.get("vendor_name"):
            raise ValueError("Vendor name not found")
        
        if not invoice_data.get("total_amount"):
            raise ValueError("Total amount not found")
        
        # Process
        logger.info("Generating journal entry")
        journal_entry = generator.process_invoice(invoice_data)
        
        logger.info(f"✓ Success: Entry #{journal_entry.id}")
        return journal_entry
        
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
    except ValueError as e:
        logger.error(f"Validation error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    
    return None

# Use it
result = process_invoice_safe("invoice.pdf")
if result:
    print("Processing successful!")
else:
    print("Processing failed - check logs")
```

## Tips and Best Practices

1. **Start with the sample invoice** to test your setup
2. **Add rules gradually** as you process more invoices
3. **Review entries regularly** to ensure accuracy
4. **Use descriptive rule names** for easier management
5. **Keep your .env file secure** (never commit it)
6. **Back up your database** regularly
7. **Monitor the logs** when running the file monitor
8. **Test with different invoice formats** to improve accuracy
9. **Adjust AI prompts** if needed for better parsing
10. **Use batch processing** for historical invoices


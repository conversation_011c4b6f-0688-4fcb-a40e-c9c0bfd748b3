"""Configuration management for AI Accountant"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Application configuration"""
    
    # AI Provider
    AI_PROVIDER = os.getenv("AI_PROVIDER", "openai")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4-turbo-preview")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-********")
    
    # Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    DATABASE_PATH = os.getenv("DATABASE_PATH", str(DATA_DIR / "accountant.db"))
    DESKTOP_PATH = os.getenv("DESKTOP_PATH", str(BASE_DIR / "files"))
    PROCESSED_DIR = DATA_DIR / "processed_invoices"
    JSON_OUTPUT_DIR = DATA_DIR / "parsed_json"
    
    # Processing
    AUTO_APPROVE = os.getenv("AUTO_APPROVE", "false").lower() == "true"
    
    # Supported file extensions
    SUPPORTED_EXTENSIONS = {".pdf", ".png", ".jpg", ".jpeg", ".tiff", ".bmp"}
    
    @classmethod
    def ensure_directories(cls):
        """Create necessary directories if they don't exist"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.PROCESSED_DIR.mkdir(exist_ok=True)
        cls.JSON_OUTPUT_DIR.mkdir(exist_ok=True)
        Path(cls.DESKTOP_PATH).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def validate(cls):
        """Validate configuration"""
        if cls.AI_PROVIDER == "openai" and not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required when AI_PROVIDER is 'openai'")
        if cls.AI_PROVIDER == "anthropic" and not cls.ANTHROPIC_API_KEY:
            raise ValueError("ANTHROPIC_API_KEY is required when AI_PROVIDER is 'anthropic'")
        if cls.AI_PROVIDER not in ["openai", "anthropic"]:
            raise ValueError("AI_PROVIDER must be 'openai' or 'anthropic'")


# Initialize directories on import
Config.ensure_directories()


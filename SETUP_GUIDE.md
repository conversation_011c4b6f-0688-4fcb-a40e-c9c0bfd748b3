# AI Accountant - Complete Setup Guide

## Overview

AI Accountant is an intelligent system that:
1. **Monitors** a folder for new invoice files (PDF, images)
2. **Parses** invoices using AI (OpenAI GPT-4 or Anthropic Claude)
3. **Extracts** structured data (vendor, amounts, line items, etc.)
4. **Saves** parsed data as JSON files
5. **Applies** accounting rules from a database
6. **Generates** double-entry journal entries
7. **Proposes** entries for your review and approval

## Prerequisites

- Python 3.8 or higher
- Tesseract OCR (for image processing)
- OpenAI API key OR Anthropic API key

## Step-by-Step Installation

### 1. Install Python Dependencies

```bash
# Create and activate virtual environment
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate

# Install packages
pip install -r requirements.txt
```

### 2. Install Tesseract OCR

**Windows:**
1. Download installer from: https://github.com/UB-Mannheim/tesseract/wiki
2. Run installer (default location: `C:\Program Files\Tesseract-OCR`)
3. Add to PATH or set in code

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
```

**macOS:**
```bash
brew install tesseract
```

### 3. Configure Environment

```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your settings
notepad .env  # Windows
nano .env     # Linux/Mac
```

**Required settings in .env:**

```env
# Choose your AI provider
AI_PROVIDER=openai  # or "anthropic"

# Add your API key
OPENAI_API_KEY=sk-your-key-here
# OR
ANTHROPIC_API_KEY=sk-ant-your-key-here

# Invoice folder (default is ./files - already created)
DESKTOP_PATH=./files

# Database location (default is fine)
DATABASE_PATH=./data/accountant.db
```

### 4. Initialize Database

```bash
python -m ai_accountant.init_db
```

This creates:
- Database tables
- Sample chart of accounts
- Sample accounting rules

## Usage

### Option 1: Automatic Monitoring (Recommended)

Start the file monitor to automatically process invoices:

```bash
python -m ai_accountant.monitor
```

The monitor will:
- Watch the configured folder for new files
- Automatically parse and process invoices
- Generate journal entries
- Move processed files to `data/processed_invoices/`

**To use:**
1. Start the monitor
2. Drop invoice files into the `./files` folder
3. Watch the console for processing updates
4. Review entries with: `python -m ai_accountant.cli review`

### Option 2: Manual Processing

Process a single invoice file:

```bash
python -m ai_accountant.cli process path/to/invoice.pdf
```

Auto-approve the entry:

```bash
python -m ai_accountant.cli process path/to/invoice.pdf --approve
```

### Review Pending Journal Entries

```bash
python -m ai_accountant.cli review
```

This will:
- Show all pending journal entries
- Display invoice details and journal lines
- Let you approve, reject, or skip each entry

## Managing Accounting Rules

### List All Rules

```bash
python -m ai_accountant.cli rules list
```

### Add Vendor Rule

Match specific vendors to accounts:

```bash
python -m ai_accountant.cli add-vendor-rule "Amazon" \
  --account "6100" \
  --name "Office Supplies" \
  --desc "Amazon purchases"
```

### Add Category Rule

Match invoice categories to accounts:

```bash
python -m ai_accountant.cli add-category-rule "Software" \
  --account "6200" \
  --name "Software & Subscriptions" \
  --desc "Software expenses"
```

## Understanding the System

### File Structure

```
ai_accountant/
├── data/
│   ├── accountant.db              # SQLite database
│   ├── parsed_json/               # Parsed invoice JSON files
│   └── processed_invoices/        # Moved invoice files
├── ai_accountant/
│   ├── config.py                  # Configuration
│   ├── models.py                  # Database models
│   ├── parser.py                  # Invoice parsing
│   ├── rules_engine.py            # Rule matching
│   ├── journal_entry.py           # Journal entry generation
│   ├── monitor.py                 # File monitoring
│   ├── cli.py                     # Command-line interface
│   └── init_db.py                 # Database setup
└── tests/                         # Unit tests
```

### How Rules Work

Rules are checked in priority order (highest first):

1. **Vendor Rules** (Priority 10): Match vendor name
   - Example: "Amazon" → Office Supplies (6100)

2. **Category Rules** (Priority 5): Match line item categories
   - Example: "Software" → Software & Subscriptions (6200)

3. **Keyword Rules**: Match keywords in descriptions

4. **Default Rule** (Priority 0): Fallback for unmatched invoices
   - Default: General Expenses (6000)

### Journal Entry Format

All entries follow double-entry bookkeeping:

```
Debit:  Expense Account (e.g., 6100 - Office Supplies)
Credit: Accounts Payable (2000)
```

Example:
```
Invoice: Amazon - $100
Debit:  6100 Office Supplies    $100.00
Credit: 2000 Accounts Payable           $100.00
```

## Sample Chart of Accounts

The system includes these default accounts:

**Assets (1000-1999)**
- 1000: Cash
- 1100: Accounts Receivable
- 1500: Equipment

**Liabilities (2000-2999)**
- 2000: Accounts Payable
- 2100: Credit Card Payable

**Equity (3000-3999)**
- 3000: Owner's Equity
- 3100: Retained Earnings

**Revenue (4000-4999)**
- 4000: Sales Revenue
- 4100: Service Revenue

**Expenses (6000-6999)**
- 6000: General Expenses
- 6100: Office Supplies
- 6200: Software & Subscriptions
- 6300: Utilities
- 6400: Rent
- 6500: Professional Services
- 6600: Marketing & Advertising
- 6700: Travel & Entertainment

## Testing

Run the test suite:

```bash
pytest tests/ -v
```

Test with sample invoice:

```bash
python -m ai_accountant.cli process sample_invoice.txt
```

## Troubleshooting

### "No module named 'ai_accountant'"
- Make sure you're in the project root directory
- Activate your virtual environment

### "OPENAI_API_KEY is required"
- Check your .env file exists
- Verify API key is set correctly
- Make sure AI_PROVIDER matches your key

### "Failed to extract text from PDF"
- Install Tesseract OCR
- Check Tesseract is in PATH
- Try with a different PDF

### "No text could be extracted"
- File might be corrupted
- Try converting to image first
- Check file format is supported

## Advanced Configuration

### Custom Account Mappings

Edit rules in database or add via CLI:

```python
from ai_accountant.rules_engine import RulesEngine

engine = RulesEngine()
engine.add_vendor_rule(
    vendor_name="Specific Vendor",
    expense_account="6XXX",
    expense_account_name="Custom Account",
    credit_account="2000",
    credit_account_name="Accounts Payable",
    priority=15  # Higher priority
)
```

### Using Different AI Models

In `.env`:

```env
# For OpenAI
OPENAI_MODEL=gpt-4-turbo-preview  # or gpt-4, gpt-3.5-turbo

# For Anthropic
ANTHROPIC_MODEL=claude-3-sonnet-********  # or claude-3-opus-********
```

## Next Steps

1. ✅ Install and configure the system
2. ✅ Initialize the database
3. ✅ Add your accounting rules
4. ✅ Start the monitor or process test invoices
5. ✅ Review and approve journal entries
6. 📊 Export to your accounting software (future feature)

## Support

For issues or questions:
- Check the troubleshooting section
- Review the code documentation
- Test with the sample invoice first


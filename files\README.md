# Invoice Input Folder

## Purpose

This folder is monitored by the AI Accountant system for new invoice files.

## How to Use

### Option 1: Automatic Monitoring

1. Start the monitor:
   ```bash
   python -m ai_accountant.monitor
   ```

2. Drop your invoice files here (PDF, PNG, JPG, etc.)

3. The system will automatically:
   - Detect the new file
   - Parse the invoice using AI
   - Extract structured data
   - Generate a journal entry
   - Move the file to `data/processed_invoices/`

4. Review pending entries:
   ```bash
   python -m ai_accountant.cli review
   ```

### Option 2: Manual Processing

Process a specific file:
```bash
python -m ai_accountant.cli process files/invoice.pdf
```

## Supported File Formats

- ✅ PDF (.pdf)
- ✅ PNG (.png)
- ✅ JPG/JPEG (.jpg, .jpeg)
- ✅ TIFF (.tiff)
- ✅ BMP (.bmp)

## What Happens to Files

1. **Before Processing**: Files stay in this folder
2. **During Processing**: System reads and parses the file
3. **After Processing**: Files are moved to `data/processed_invoices/`

## Tips

- Use clear, readable invoice files for best results
- PDFs with text are processed faster than scanned images
- File names don't matter - the system reads the content
- You can drop multiple files at once

## Example Workflow

```bash
# 1. Start the monitor
python -m ai_accountant.monitor

# 2. Drop invoices in this folder
# (Copy your invoice files here)

# 3. Watch the console for processing updates

# 4. Review and approve entries
python -m ai_accountant.cli review
```

## Troubleshooting

**Files not being processed?**
- Make sure the monitor is running
- Check file format is supported
- Look for error messages in console

**Processing errors?**
- Verify the file is not corrupted
- Try with a different file format
- Check if Tesseract OCR is installed (for images)

## Location

This folder is located at: `./files/` (relative to project root)

You can change this location by editing the `DESKTOP_PATH` in your `.env` file.


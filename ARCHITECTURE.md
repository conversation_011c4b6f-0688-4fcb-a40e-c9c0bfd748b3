# AI Accountant - System Architecture

## Overview

AI Accountant is a modular Python application that automates invoice processing and journal entry generation using AI and rule-based accounting logic.

## System Components

```
┌─────────────────────────────────────────────────────────────┐
│                     AI Accountant System                     │
└─────────────────────────────────────────────────────────────┘

┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│   Desktop    │      │   Invoice    │      │   Database   │
│   Monitor    │─────▶│   Parser     │─────▶│   Storage    │
│  (watchdog)  │      │  (AI + OCR)  │      │  (SQLite)    │
└──────────────┘      └──────────────┘      └──────────────┘
                             │
                             ▼
                      ┌──────────────┐
                      │    Rules     │
                      │    Engine    │
                      └──────────────┘
                             │
                             ▼
                      ┌──────────────┐
                      │   Journal    │
                      │    Entry     │
                      │  Generator   │
                      └──────────────┘
                             │
                             ▼
                      ┌──────────────┐
                      │     CLI      │
                      │   Review &   │
                      │   Approve    │
                      └──────────────┘
```

## Module Descriptions

### 1. Configuration (`config.py`)

**Purpose:** Centralized configuration management

**Key Features:**
- Environment variable loading
- Path management
- API key validation
- Directory initialization

**Configuration Sources:**
- `.env` file
- Environment variables
- Default values

### 2. Database Models (`models.py`)

**Purpose:** SQLAlchemy ORM models for data persistence

**Tables:**
- `invoices`: Parsed invoice data
- `invoice_line_items`: Individual line items
- `accounting_rules`: Rule definitions
- `journal_entries`: Generated journal entries
- `journal_entry_lines`: Debit/credit lines
- `chart_of_accounts`: Account definitions

**Relationships:**
- Invoice → Line Items (1:N)
- Invoice → Journal Entries (1:N)
- Journal Entry → Entry Lines (1:N)

### 3. Invoice Parser (`parser.py`)

**Purpose:** Extract structured data from invoice files

**Process Flow:**
1. Detect file type (PDF or image)
2. Extract text using:
   - PyPDF2 for text-based PDFs
   - pdf2image + Tesseract for scanned PDFs
   - Tesseract for images
3. Send text to AI (OpenAI or Anthropic)
4. Parse AI response into structured JSON
5. Save JSON to disk

**Extracted Fields:**
- Vendor name
- Invoice number and date
- Due date
- Amounts (subtotal, tax, total)
- Currency
- Line items with descriptions, quantities, prices

**AI Prompt Engineering:**
- Structured output format
- Field validation
- Category suggestions
- Error handling

### 4. Rules Engine (`rules_engine.py`)

**Purpose:** Match invoices to accounting rules

**Rule Types:**
1. **Vendor Rules** (Priority 10)
   - Match by vendor name
   - Exact or partial matching
   - Regex pattern support

2. **Category Rules** (Priority 5)
   - Match by line item category
   - Description keyword matching
   - Regex pattern support

3. **Keyword Rules**
   - Match keywords anywhere
   - Flexible matching

4. **Default Rule** (Priority 0)
   - Fallback for unmatched invoices

**Matching Algorithm:**
```python
1. Load all active rules ordered by priority (DESC)
2. For each rule:
   a. Check if rule matches invoice data
   b. If match found, return rule
3. If no match, return None (use default)
```

**Account Mapping:**
- Debit account (expense)
- Credit account (liability)
- Account names and descriptions

### 5. Journal Entry Generator (`journal_entry.py`)

**Purpose:** Create double-entry journal entries

**Process:**
1. Save invoice to database
2. Get account mapping from rules engine
3. Create journal entry header
4. Create debit line (expense account)
5. Create credit line (accounts payable)
6. Validate entry is balanced

**Double-Entry Bookkeeping:**
```
For a $100 invoice from Amazon:

Debit:  6100 Office Supplies    $100.00
Credit: 2000 Accounts Payable           $100.00
        ─────────────────────   ───────
        Total                   $100.00  $100.00
```

**Status Workflow:**
```
parsed → proposed → approved/rejected
```

### 6. Desktop Monitor (`monitor.py`)

**Purpose:** Automatic file detection and processing

**Technology:** Watchdog library for file system events

**Event Handling:**
1. Detect new file creation
2. Verify file extension
3. Wait for file write completion
4. Process invoice
5. Move to processed folder

**Features:**
- Duplicate detection
- Error handling
- Progress logging
- Graceful shutdown

### 7. CLI Interface (`cli.py`)

**Purpose:** User interaction and management

**Commands:**

```bash
# Process single invoice
cli process <file> [--approve]

# Review pending entries
cli review

# Manage rules
cli rules list
cli add-vendor-rule <vendor> --account <code> --name <name>
cli add-category-rule <category> --account <code> --name <name>
```

**Features:**
- Rich terminal UI
- Interactive approval workflow
- Formatted tables and panels
- Color-coded output

### 8. Database Initialization (`init_db.py`)

**Purpose:** Setup database with initial data

**Creates:**
- All database tables
- Sample chart of accounts
- Default accounting rules
- Vendor and category rules

## Data Flow

### Automatic Processing Flow

```
1. User drops invoice.pdf in monitored folder
   ↓
2. Monitor detects new file
   ↓
3. Parser extracts text (OCR if needed)
   ↓
4. AI parses text into structured JSON
   ↓
5. JSON saved to data/parsed_json/
   ↓
6. Invoice saved to database
   ↓
7. Rules engine finds matching rule
   ↓
8. Journal entry generated (proposed status)
   ↓
9. File moved to data/processed_invoices/
   ↓
10. User reviews via CLI
   ↓
11. User approves/rejects entry
   ↓
12. Entry status updated (approved/rejected)
```

### Manual Processing Flow

```
1. User runs: cli process invoice.pdf
   ↓
2. Same steps 3-8 as above
   ↓
3. Entry displayed in terminal
   ↓
4. User can approve with --approve flag
   OR review later with: cli review
```

## Database Schema

### Invoices Table
```sql
- id (PK)
- file_path, file_name
- processed_at
- vendor_name, invoice_number
- invoice_date, due_date
- subtotal, tax_amount, total_amount
- currency
- raw_json (full parsed data)
- status (parsed/processed/approved/rejected)
```

### Accounting Rules Table
```sql
- id (PK)
- rule_type (vendor/category/keyword/default)
- match_value, match_pattern
- debit_account, debit_account_name
- credit_account, credit_account_name
- description, priority
- is_active, created_at
```

### Journal Entries Table
```sql
- id (PK)
- invoice_id (FK)
- entry_date, description, reference
- status (proposed/approved/rejected/posted)
- approved_at, approved_by
```

### Journal Entry Lines Table
```sql
- id (PK)
- journal_entry_id (FK)
- account_code, account_name
- description
- debit, credit
```

## Technology Stack

**Core:**
- Python 3.8+
- SQLAlchemy (ORM)
- SQLite (Database)

**AI/ML:**
- OpenAI GPT-4 (or GPT-3.5)
- Anthropic Claude 3
- Tesseract OCR

**File Processing:**
- PyPDF2 (PDF text extraction)
- pdf2image (PDF to image conversion)
- Pillow (Image processing)
- pytesseract (OCR wrapper)

**Monitoring:**
- watchdog (File system events)

**CLI:**
- typer (Command-line interface)
- rich (Terminal formatting)

**Utilities:**
- python-dotenv (Environment variables)
- pydantic (Data validation)
- python-dateutil (Date parsing)

## Design Patterns

### 1. Dependency Injection
- Session objects passed to constructors
- Allows for testing with mock sessions

### 2. Strategy Pattern
- AI provider selection (OpenAI vs Anthropic)
- Configurable via environment

### 3. Observer Pattern
- File system monitoring
- Event-driven processing

### 4. Repository Pattern
- Database access through ORM
- Abstraction over data storage

## Error Handling

**Parser Errors:**
- File not found
- Unsupported format
- OCR failure
- AI parsing failure

**Processing Errors:**
- Database errors
- Rule matching failures
- Invalid data

**Recovery:**
- Detailed error logging
- File remains in place on error
- Manual retry possible

## Security Considerations

1. **API Keys:** Stored in .env (not committed)
2. **Database:** Local SQLite (no network exposure)
3. **File Access:** Limited to configured directories
4. **Input Validation:** Pydantic models, file type checks

## Performance

**Bottlenecks:**
- AI API calls (2-5 seconds per invoice)
- OCR processing (1-3 seconds for images)
- PDF conversion (1-2 seconds)

**Optimizations:**
- Direct PDF text extraction when possible
- Caching of parsed results
- Batch processing capability

## Future Enhancements

1. **Export Integration:**
   - QuickBooks export
   - Xero integration
   - CSV export

2. **Advanced Rules:**
   - Amount-based rules
   - Date-based rules
   - Multi-condition rules

3. **Web Interface:**
   - Dashboard for monitoring
   - Web-based approval
   - Rule management UI

4. **Machine Learning:**
   - Learn from approvals
   - Suggest new rules
   - Improve categorization

5. **Multi-Currency:**
   - Exchange rate handling
   - Currency conversion

6. **Audit Trail:**
   - Change history
   - User actions log
   - Compliance reporting


"""Tests for rules engine"""

import pytest
from ai_accountant.models import init_database, get_session
from ai_accountant.rules_engine import RulesEngine


@pytest.fixture
def session():
    """Create test database session"""
    init_database()
    return get_session()


@pytest.fixture
def rules_engine(session):
    """Create rules engine instance"""
    return RulesEngine(session)


def test_add_vendor_rule(rules_engine):
    """Test adding a vendor rule"""
    rule = rules_engine.add_vendor_rule(
        "Test Vendor",
        "6100",
        "Office Supplies",
        "Test rule"
    )
    
    assert rule.id is not None
    assert rule.rule_type == "vendor"
    assert rule.match_value == "Test Vendor"
    assert rule.debit_account == "6100"


def test_find_matching_vendor_rule(rules_engine):
    """Test finding a matching vendor rule"""
    # Add a rule
    rules_engine.add_vendor_rule(
        "Amazon",
        "6100",
        "Office Supplies",
        "Amazon purchases"
    )
    
    # Test invoice data
    invoice_data = {
        "vendor_name": "Amazon Web Services",
        "line_items": []
    }
    
    # Should match
    rule = rules_engine.find_matching_rule(invoice_data)
    assert rule is not None
    assert rule.match_value == "Amazon"


def test_find_matching_category_rule(rules_engine):
    """Test finding a matching category rule"""
    # Add a rule
    rules_engine.add_category_rule(
        "Software",
        "6200",
        "Software & Subscriptions",
        "Software expenses"
    )
    
    # Test invoice data
    invoice_data = {
        "vendor_name": "Some Vendor",
        "line_items": [
            {"description": "Software License", "category": "Software", "amount": 100}
        ]
    }
    
    # Should match
    rule = rules_engine.find_matching_rule(invoice_data)
    assert rule is not None
    assert rule.match_value == "Software"


def test_default_rule(rules_engine):
    """Test default rule fallback"""
    # Add default rule
    rules_engine.add_default_rule()
    
    # Test invoice with no matches
    invoice_data = {
        "vendor_name": "Unknown Vendor",
        "line_items": []
    }
    
    # Should match default rule
    rule = rules_engine.find_matching_rule(invoice_data)
    assert rule is not None
    assert rule.rule_type == "default"


def test_get_account_mapping(rules_engine):
    """Test getting account mapping"""
    # Add a rule
    rules_engine.add_vendor_rule(
        "Test Corp",
        "6300",
        "Utilities",
        "Utility bills"
    )
    
    invoice_data = {
        "vendor_name": "Test Corp",
        "line_items": []
    }
    
    mapping = rules_engine.get_account_mapping(invoice_data)
    
    assert mapping["debit_account"] == "6300"
    assert mapping["debit_account_name"] == "Utilities"
    assert mapping["credit_account"] == "2000"
    assert mapping["credit_account_name"] == "Accounts Payable"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])


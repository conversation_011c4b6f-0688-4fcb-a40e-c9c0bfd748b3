# Getting Started with AI Accountant

## Quick Start (5 Minutes)

### 1. Install Dependencies

```bash
# Create virtual environment
python -m venv venv

# Activate it
venv\Scripts\activate  # Windows
# OR
source venv/bin/activate  # Linux/Mac

# Install packages
pip install -r requirements.txt
```

### 2. Install Tesseract OCR

**Windows:** Download and install from https://github.com/UB-Mannheim/tesseract/wiki

**Linux:** `sudo apt-get install tesseract-ocr`

**Mac:** `brew install tesseract`

### 3. Configure API Key

```bash
# Copy example config
cp .env.example .env

# Edit .env and add your API key
# For OpenAI:
AI_PROVIDER=openai
OPENAI_API_KEY=sk-your-key-here

# OR for Anthropic:
AI_PROVIDER=anthropic
ANTHROPIC_API_KEY=sk-ant-your-key-here
```

### 4. Initialize Database

```bash
python -m ai_accountant.init_db
```

### 5. Test with Sample Invoice

```bash
python -m ai_accountant.cli process sample_invoice.txt
```

You should see:
- ✓ Invoice parsed
- ✓ Journal entry created
- A formatted table showing the journal entry

### 6. Review and Approve

```bash
python -m ai_accountant.cli review
```

Type `a` to approve the entry!

## What Just Happened?

1. **Parsed the invoice** using AI to extract:
   - Vendor: Amazon Web Services
   - Total: $68.19
   - Line items with descriptions

2. **Applied accounting rules** to determine:
   - Debit: 6100 Office Supplies
   - Credit: 2000 Accounts Payable

3. **Created a journal entry** following double-entry bookkeeping

4. **Saved everything** to the database

## Next Steps

### Option A: Automatic Monitoring

Set up automatic processing:

```bash
# 1. Start the monitor (watches ./files folder by default)
python -m ai_accountant.monitor

# 2. Drop invoice files into the ./files folder
# They'll be processed automatically!
```

### Option B: Manual Processing

Process invoices one at a time:

```bash
python -m ai_accountant.cli process path/to/invoice.pdf
```

## Customizing for Your Business

### Add Your Vendors

```bash
# Example: Add a rule for your internet provider
python -m ai_accountant.cli add-vendor-rule "Comcast" \
  --account "6300" \
  --name "Utilities" \
  --desc "Internet service"

# Example: Add a rule for your software vendors
python -m ai_accountant.cli add-vendor-rule "Adobe" \
  --account "6200" \
  --name "Software & Subscriptions" \
  --desc "Creative Cloud"
```

### Add Category Rules

```bash
# Example: Marketing expenses
python -m ai_accountant.cli add-category-rule "Advertising" \
  --account "6600" \
  --name "Marketing & Advertising" \
  --desc "Online ads and marketing"

# Example: Travel expenses
python -m ai_accountant.cli add-category-rule "Travel" \
  --account "6700" \
  --name "Travel & Entertainment" \
  --desc "Business travel"
```

### View Your Rules

```bash
python -m ai_accountant.cli rules list
```

## Understanding the Output

### Parsed JSON Files

Location: `data/parsed_json/`

Each invoice creates a JSON file with structured data:

```json
{
  "vendor_name": "Amazon Web Services",
  "invoice_number": "INV-2024-001",
  "total_amount": 68.19,
  "line_items": [...]
}
```

### Processed Invoices

Location: `data/processed_invoices/`

Original invoice files are moved here after processing.

### Input Folder

Location: `files/`

Drop your invoice files here for processing. The monitor watches this folder.

### Database

Location: `data/accountant.db`

SQLite database containing:
- All invoices
- Journal entries
- Accounting rules
- Chart of accounts

## Common Workflows

### Daily Processing

```bash
# Morning: Start the monitor
python -m ai_accountant.monitor

# Throughout the day: Drop invoices in the folder
# (They process automatically)

# End of day: Review and approve
python -m ai_accountant.cli review
```

### Weekly Batch Processing

```bash
# Process all invoices in a folder
for file in invoices/*.pdf; do
    python -m ai_accountant.cli process "$file"
done

# Review all at once
python -m ai_accountant.cli review
```

### Monthly Reconciliation

```python
# Export journal entries to CSV
from ai_accountant.models import get_session, JournalEntry
import csv

session = get_session()
entries = session.query(JournalEntry).filter(
    JournalEntry.status == "approved"
).all()

# Export and import to your accounting software
```

## Troubleshooting

### "No module named 'ai_accountant'"

**Solution:** Make sure you're in the project root directory

```bash
cd d:\my dreams\robotics\ai_accountant
python -m ai_accountant.cli process sample_invoice.txt
```

### "OPENAI_API_KEY is required"

**Solution:** Check your .env file

```bash
# Make sure .env exists
ls .env

# Check contents
cat .env

# Verify API key is set (not the example value)
```

### "Failed to extract text from PDF"

**Solution:** Install Tesseract OCR

```bash
# Windows: Download installer
# Linux: sudo apt-get install tesseract-ocr
# Mac: brew install tesseract

# Verify installation
tesseract --version
```

### Invoice Not Parsing Correctly

**Solution:** Check the AI response

1. Look at the parsed JSON file in `data/parsed_json/`
2. If fields are missing, the AI might need better input
3. Try with a clearer/higher quality invoice image
4. Consider adjusting the AI prompt in `parser.py`

### Wrong Account Assignment

**Solution:** Add or adjust rules

```bash
# Check current rules
python -m ai_accountant.cli rules list

# Add a more specific rule with higher priority
python -m ai_accountant.cli add-vendor-rule "Vendor Name" \
  --account "XXXX" \
  --name "Account Name"
```

## System Check

Run the system check to verify everything is set up:

```bash
python quickstart.py
```

This checks:
- ✓ Python version
- ✓ Virtual environment
- ✓ Dependencies installed
- ✓ Environment file exists
- ✓ API keys configured
- ✓ Tesseract installed
- ✓ Database initialized

## Learning More

- **README.md** - Overview and features
- **SETUP_GUIDE.md** - Detailed installation instructions
- **ARCHITECTURE.md** - System design and components
- **EXAMPLES.md** - Code examples and use cases

## Getting Help

1. Check the troubleshooting section above
2. Review the documentation files
3. Test with `sample_invoice.txt` first
4. Check the console output for error messages
5. Verify your .env configuration

## Tips for Success

1. **Start small** - Process a few invoices manually first
2. **Build your rules** - Add rules as you encounter new vendors
3. **Review regularly** - Don't let pending entries pile up
4. **Back up your database** - Copy `data/accountant.db` regularly
5. **Keep invoices organized** - Use consistent file naming
6. **Monitor the logs** - Watch for parsing errors
7. **Test different formats** - PDFs, images, scanned documents
8. **Adjust as needed** - Fine-tune rules based on results

## What's Next?

Once you're comfortable with the basics:

1. **Automate your workflow** - Set up the monitor to run on startup
2. **Integrate with accounting software** - Export to QuickBooks, Xero, etc.
3. **Customize the chart of accounts** - Match your business structure
4. **Add advanced rules** - Multi-condition rules, amount thresholds
5. **Build reports** - Query the database for insights
6. **Share with your team** - Set up for multiple users

## Success Checklist

- [ ] Virtual environment created and activated
- [ ] Dependencies installed
- [ ] Tesseract OCR installed
- [ ] .env file configured with API key
- [ ] Database initialized
- [ ] Sample invoice processed successfully
- [ ] First journal entry reviewed and approved
- [ ] Custom vendor rules added
- [ ] Monitor tested (optional)
- [ ] Backup strategy in place

Congratulations! You're ready to automate your invoice processing! 🎉


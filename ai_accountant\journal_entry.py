"""Journal entry generation"""

from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from ai_accountant.models import (
    Invoice, InvoiceLineItem, JournalEntry, JournalEntryLine, 
    get_session
)
from ai_accountant.rules_engine import RulesEngine


class JournalEntryGenerator:
    """Generate journal entries from parsed invoices"""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session or get_session()
        self.rules_engine = RulesEngine(self.session)
    
    def process_invoice(self, invoice_data: Dict[str, Any]) -> JournalEntry:
        """
        Process a parsed invoice and generate journal entry
        
        Args:
            invoice_data: Parsed invoice data dictionary
            
        Returns:
            Generated JournalEntry object
        """
        # Save invoice to database
        invoice = self._save_invoice(invoice_data)
        
        # Get account mapping from rules
        account_mapping = self.rules_engine.get_account_mapping(invoice_data)
        
        # Create journal entry
        journal_entry = self._create_journal_entry(invoice, invoice_data, account_mapping)
        
        return journal_entry
    
    def _save_invoice(self, invoice_data: Dict[str, Any]) -> Invoice:
        """Save invoice to database"""
        import json
        
        invoice = Invoice(
            file_path=invoice_data.get("file_path", ""),
            file_name=invoice_data.get("file_name", ""),
            vendor_name=invoice_data.get("vendor_name"),
            invoice_number=invoice_data.get("invoice_number"),
            invoice_date=invoice_data.get("invoice_date"),
            due_date=invoice_data.get("due_date"),
            subtotal=invoice_data.get("subtotal"),
            tax_amount=invoice_data.get("tax_amount"),
            total_amount=invoice_data.get("total_amount"),
            currency=invoice_data.get("currency", "USD"),
            raw_json=json.dumps(invoice_data),
            status="parsed"
        )
        
        self.session.add(invoice)
        self.session.flush()  # Get invoice ID
        
        # Save line items
        for item_data in invoice_data.get("line_items", []):
            line_item = InvoiceLineItem(
                invoice_id=invoice.id,
                description=item_data.get("description"),
                quantity=item_data.get("quantity"),
                unit_price=item_data.get("unit_price"),
                amount=item_data.get("amount"),
                category=item_data.get("category")
            )
            self.session.add(line_item)
        
        self.session.commit()
        return invoice
    
    def _create_journal_entry(self, invoice: Invoice, invoice_data: Dict[str, Any], 
                             account_mapping: Dict[str, str]) -> JournalEntry:
        """Create journal entry with debit and credit lines"""
        
        total_amount = invoice_data.get("total_amount", 0)
        vendor_name = invoice_data.get("vendor_name", "Unknown Vendor")
        invoice_number = invoice_data.get("invoice_number", "N/A")
        
        # Create journal entry header
        journal_entry = JournalEntry(
            invoice_id=invoice.id,
            description=f"Invoice from {vendor_name}",
            reference=invoice_number,
            status="proposed"
        )
        
        self.session.add(journal_entry)
        self.session.flush()  # Get journal entry ID
        
        # Create debit line (Expense)
        debit_line = JournalEntryLine(
            journal_entry_id=journal_entry.id,
            account_code=account_mapping["debit_account"],
            account_name=account_mapping["debit_account_name"],
            description=f"{vendor_name} - {invoice_number}",
            debit=total_amount,
            credit=0.0
        )
        self.session.add(debit_line)
        
        # Create credit line (Accounts Payable)
        credit_line = JournalEntryLine(
            journal_entry_id=journal_entry.id,
            account_code=account_mapping["credit_account"],
            account_name=account_mapping["credit_account_name"],
            description=f"{vendor_name} - {invoice_number}",
            debit=0.0,
            credit=total_amount
        )
        self.session.add(credit_line)
        
        self.session.commit()
        
        return journal_entry
    
    def approve_journal_entry(self, entry_id: int, approved_by: str = "user") -> JournalEntry:
        """Approve a proposed journal entry"""
        entry = self.session.query(JournalEntry).get(entry_id)
        
        if not entry:
            raise ValueError(f"Journal entry {entry_id} not found")
        
        entry.status = "approved"
        entry.approved_at = datetime.utcnow()
        entry.approved_by = approved_by
        
        # Update invoice status
        if entry.invoice:
            entry.invoice.status = "approved"
        
        self.session.commit()
        return entry
    
    def reject_journal_entry(self, entry_id: int) -> JournalEntry:
        """Reject a proposed journal entry"""
        entry = self.session.query(JournalEntry).get(entry_id)
        
        if not entry:
            raise ValueError(f"Journal entry {entry_id} not found")
        
        entry.status = "rejected"
        
        # Update invoice status
        if entry.invoice:
            entry.invoice.status = "rejected"
        
        self.session.commit()
        return entry
    
    def get_pending_entries(self):
        """Get all pending journal entries"""
        return self.session.query(JournalEntry).filter(
            JournalEntry.status == "proposed"
        ).all()
    
    def get_entry_details(self, entry_id: int) -> Dict[str, Any]:
        """Get detailed information about a journal entry"""
        entry = self.session.query(JournalEntry).get(entry_id)
        
        if not entry:
            return None
        
        return {
            "id": entry.id,
            "date": entry.entry_date.isoformat(),
            "description": entry.description,
            "reference": entry.reference,
            "status": entry.status,
            "invoice": {
                "vendor": entry.invoice.vendor_name,
                "invoice_number": entry.invoice.invoice_number,
                "total": entry.invoice.total_amount,
                "currency": entry.invoice.currency
            } if entry.invoice else None,
            "lines": [
                {
                    "account_code": line.account_code,
                    "account_name": line.account_name,
                    "description": line.description,
                    "debit": line.debit,
                    "credit": line.credit
                }
                for line in entry.lines
            ]
        }


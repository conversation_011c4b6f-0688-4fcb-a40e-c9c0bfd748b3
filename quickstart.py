"""Quick start script for AI Accountant"""

import os
import sys
from pathlib import Path


def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True


def check_venv():
    """Check if virtual environment is activated"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ Virtual environment activated")
        return True
    else:
        print("⚠ Virtual environment not activated")
        print("  Run: venv\\Scripts\\activate (Windows) or source venv/bin/activate (Linux/Mac)")
        return False


def check_dependencies():
    """Check if dependencies are installed"""
    try:
        import dotenv
        import openai
        import anthropic
        import pytesseract
        import PIL
        import PyPDF2
        import pdf2image
        import sqlalchemy
        import watchdog
        import rich
        import typer
        print("✓ All dependencies installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e.name}")
        print("  Run: pip install -r requirements.txt")
        return False


def check_env_file():
    """Check if .env file exists"""
    if Path(".env").exists():
        print("✓ .env file exists")
        return True
    else:
        print("⚠ .env file not found")
        print("  Run: cp .env.example .env")
        print("  Then edit .env with your API keys")
        return False


def check_api_keys():
    """Check if API keys are configured"""
    from dotenv import load_dotenv
    load_dotenv()
    
    provider = os.getenv("AI_PROVIDER", "openai")
    
    if provider == "openai":
        key = os.getenv("OPENAI_API_KEY")
        if key and key != "your_openai_api_key_here":
            print(f"✓ OpenAI API key configured")
            return True
        else:
            print("❌ OpenAI API key not configured")
            print("  Edit .env and set OPENAI_API_KEY")
            return False
    elif provider == "anthropic":
        key = os.getenv("ANTHROPIC_API_KEY")
        if key and key != "your_anthropic_api_key_here":
            print(f"✓ Anthropic API key configured")
            return True
        else:
            print("❌ Anthropic API key not configured")
            print("  Edit .env and set ANTHROPIC_API_KEY")
            return False
    else:
        print(f"❌ Invalid AI_PROVIDER: {provider}")
        return False


def check_database():
    """Check if database is initialized"""
    from ai_accountant.config import Config
    if Path(Config.DATABASE_PATH).exists():
        print("✓ Database initialized")
        return True
    else:
        print("⚠ Database not initialized")
        print("  Run: python -m ai_accountant.init_db")
        return False


def check_tesseract():
    """Check if Tesseract is installed"""
    try:
        import pytesseract
        pytesseract.get_tesseract_version()
        print("✓ Tesseract OCR installed")
        return True
    except Exception:
        print("⚠ Tesseract OCR not found")
        print("  Install from: https://github.com/UB-Mannheim/tesseract/wiki")
        return False


def main():
    """Run all checks"""
    print("\n🔍 AI Accountant - System Check\n")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Virtual Environment", check_venv),
        ("Dependencies", check_dependencies),
        ("Environment File", check_env_file),
        ("API Keys", check_api_keys),
        ("Tesseract OCR", check_tesseract),
        ("Database", check_database),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Error checking {name}: {e}")
            results.append(False)
        print()
    
    print("=" * 50)
    
    if all(results):
        print("\n✅ All checks passed! System is ready.\n")
        print("Next steps:")
        print("  • Start monitor: python -m ai_accountant.monitor")
        print("  • Process file:  python -m ai_accountant.cli process <file>")
        print("  • Review entries: python -m ai_accountant.cli review")
        print()
        return 0
    else:
        print("\n⚠ Some checks failed. Please fix the issues above.\n")
        print("See SETUP_GUIDE.md for detailed instructions.")
        print()
        return 1


if __name__ == "__main__":
    exit(main())


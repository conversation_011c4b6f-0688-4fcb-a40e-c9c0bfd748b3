"""Rules engine for matching invoices to accounting rules"""

import re
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from ai_accountant.models import AccountingRule, get_session


class RulesEngine:
    """Apply accounting rules to parsed invoice data"""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session or get_session()
    
    def find_matching_rule(self, invoice_data: Dict[str, Any]) -> Optional[AccountingRule]:
        """
        Find the best matching accounting rule for an invoice
        
        Args:
            invoice_data: Parsed invoice data
            
        Returns:
            Matching AccountingRule or None
        """
        vendor_name = invoice_data.get("vendor_name", "").lower()
        line_items = invoice_data.get("line_items", [])
        
        # Get all active rules ordered by priority
        rules = self.session.query(AccountingRule).filter(
            AccountingRule.is_active == True
        ).order_by(AccountingRule.priority.desc()).all()
        
        for rule in rules:
            if self._rule_matches(rule, vendor_name, line_items):
                return rule
        
        return None
    
    def _rule_matches(self, rule: AccountingRule, vendor_name: str, line_items: List[Dict]) -> bool:
        """Check if a rule matches the invoice data"""
        
        if rule.rule_type == "vendor":
            # Match vendor name
            match_value = rule.match_value.lower() if rule.match_value else ""
            if match_value in vendor_name or vendor_name in match_value:
                return True
            
            # Try regex pattern if provided
            if rule.match_pattern:
                try:
                    if re.search(rule.match_pattern, vendor_name, re.IGNORECASE):
                        return True
                except re.error:
                    pass
        
        elif rule.rule_type == "category":
            # Match category in line items
            match_value = rule.match_value.lower() if rule.match_value else ""
            for item in line_items:
                category = item.get("category", "").lower()
                description = item.get("description", "").lower()
                
                if match_value in category or match_value in description:
                    return True
                
                # Try regex pattern
                if rule.match_pattern:
                    try:
                        if re.search(rule.match_pattern, category, re.IGNORECASE) or \
                           re.search(rule.match_pattern, description, re.IGNORECASE):
                            return True
                    except re.error:
                        pass
        
        elif rule.rule_type == "keyword":
            # Match keyword in any text field
            match_value = rule.match_value.lower() if rule.match_value else ""
            
            # Check vendor name
            if match_value in vendor_name:
                return True
            
            # Check line items
            for item in line_items:
                description = item.get("description", "").lower()
                if match_value in description:
                    return True
        
        elif rule.rule_type == "default":
            # Default rule always matches (should have lowest priority)
            return True
        
        return False
    
    def get_account_mapping(self, invoice_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Get account mapping for an invoice
        
        Returns:
            Dictionary with debit_account, debit_account_name, credit_account, credit_account_name
        """
        rule = self.find_matching_rule(invoice_data)
        
        if rule:
            return {
                "debit_account": rule.debit_account,
                "debit_account_name": rule.debit_account_name,
                "credit_account": rule.credit_account,
                "credit_account_name": rule.credit_account_name,
                "rule_description": rule.description,
                "rule_id": rule.id
            }
        
        # Default fallback
        return {
            "debit_account": "6000",
            "debit_account_name": "General Expenses",
            "credit_account": "2000",
            "credit_account_name": "Accounts Payable",
            "rule_description": "Default rule - no specific match found",
            "rule_id": None
        }
    
    def add_vendor_rule(self, vendor_name: str, expense_account: str, 
                       expense_account_name: str, description: str = "",
                       credit_account: str = "2000", 
                       credit_account_name: str = "Accounts Payable",
                       priority: int = 10) -> AccountingRule:
        """Add a new vendor-based rule"""
        rule = AccountingRule(
            rule_type="vendor",
            match_value=vendor_name,
            debit_account=expense_account,
            debit_account_name=expense_account_name,
            credit_account=credit_account,
            credit_account_name=credit_account_name,
            description=description or f"Rule for vendor: {vendor_name}",
            priority=priority
        )
        self.session.add(rule)
        self.session.commit()
        return rule
    
    def add_category_rule(self, category: str, expense_account: str,
                         expense_account_name: str, description: str = "",
                         credit_account: str = "2000",
                         credit_account_name: str = "Accounts Payable",
                         priority: int = 5) -> AccountingRule:
        """Add a new category-based rule"""
        rule = AccountingRule(
            rule_type="category",
            match_value=category,
            debit_account=expense_account,
            debit_account_name=expense_account_name,
            credit_account=credit_account,
            credit_account_name=credit_account_name,
            description=description or f"Rule for category: {category}",
            priority=priority
        )
        self.session.add(rule)
        self.session.commit()
        return rule
    
    def add_default_rule(self, expense_account: str = "6000",
                        expense_account_name: str = "General Expenses",
                        credit_account: str = "2000",
                        credit_account_name: str = "Accounts Payable") -> AccountingRule:
        """Add a default fallback rule"""
        rule = AccountingRule(
            rule_type="default",
            match_value="*",
            debit_account=expense_account,
            debit_account_name=expense_account_name,
            credit_account=credit_account,
            credit_account_name=credit_account_name,
            description="Default rule for unmatched invoices",
            priority=0
        )
        self.session.add(rule)
        self.session.commit()
        return rule
    
    def list_rules(self) -> List[AccountingRule]:
        """List all active rules"""
        return self.session.query(AccountingRule).filter(
            AccountingRule.is_active == True
        ).order_by(AccountingRule.priority.desc()).all()
    
    def delete_rule(self, rule_id: int):
        """Delete a rule"""
        rule = self.session.query(AccountingRule).get(rule_id)
        if rule:
            self.session.delete(rule)
            self.session.commit()


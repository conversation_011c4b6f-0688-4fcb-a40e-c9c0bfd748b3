# 🚀 START HERE - AI Accountant

Welcome to **AI Accountant**! This is your complete guide to getting started.

## 📖 What is AI Accountant?

AI Accountant is an intelligent system that:
1. **Watches** the `./files` folder for invoice files (PDF, images)
2. **Reads** invoices using AI (like a human accountant would)
3. **Extracts** all the important data (vendor, amounts, dates, items)
4. **Saves** the data as JSON files
5. **Applies** your accounting rules (which accounts to use)
6. **Creates** proper journal entries (debit/credit)
7. **Proposes** entries for you to review and approve

**Result:** You save hours of manual data entry and reduce errors!

## ⚡ Quick Start (Choose Your Path)

### Path A: I Want to Test It Right Now (5 minutes)

```bash
# 1. Install dependencies
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt

# 2. Set up your API key
cp .env.example .env
# Edit .env and add your OpenAI or Anthropic API key

# 3. Initialize database
python -m ai_accountant.init_db

# 4. Test with sample invoice
python -m ai_accountant.cli process sample_invoice.txt

# 5. Review the result
python -m ai_accountant.cli review
```

**Done!** You just processed your first invoice with AI! 🎉

### Path B: I Want to Set Up for Real Use (15 minutes)

Follow the complete setup guide: **[GETTING_STARTED.md](GETTING_STARTED.md)**

This includes:
- Installing Tesseract OCR (for image processing)
- Configuring your desktop folder
- Adding your business rules
- Setting up automatic monitoring

### Path C: I Want to Understand Everything First

Read these in order:
1. **[PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)** - Overview of features and capabilities
2. **[ARCHITECTURE.md](ARCHITECTURE.md)** - How the system works
3. **[SETUP_GUIDE.md](SETUP_GUIDE.md)** - Detailed installation
4. **[EXAMPLES.md](EXAMPLES.md)** - Usage examples

## 📚 Documentation Guide

| Document | Purpose | When to Read |
|----------|---------|--------------|
| **START_HERE.md** | You are here! | Read first |
| **GETTING_STARTED.md** | Quick setup guide | When ready to install |
| **PROJECT_SUMMARY.md** | Feature overview | To understand capabilities |
| **SETUP_GUIDE.md** | Detailed installation | For complete setup |
| **ARCHITECTURE.md** | System design | To understand internals |
| **EXAMPLES.md** | Code examples | When using the system |
| **README.md** | Project overview | General reference |

## 🎯 What You Need

### Required
- ✅ Python 3.8 or higher
- ✅ OpenAI API key OR Anthropic API key
- ✅ 10 minutes of your time

### Optional (but recommended)
- ⭐ Tesseract OCR (for processing image invoices)
- ⭐ Sample invoices to test with

## 💰 Cost Estimate

**API Costs:**
- OpenAI GPT-4: ~$0.01-0.03 per invoice
- Anthropic Claude: ~$0.01-0.02 per invoice

**Example:** Processing 100 invoices/month = $1-3/month

Compare to: Manual data entry at $20/hour × 2 minutes per invoice = $33/month

**Savings: ~$30/month** (plus reduced errors!)

## 🎬 Your First Invoice in 3 Steps

### Step 1: Install and Configure

```bash
# Install
pip install -r requirements.txt

# Configure (add your API key)
cp .env.example .env
notepad .env  # Windows
nano .env     # Linux/Mac

# Initialize
python -m ai_accountant.init_db
```

### Step 2: Process Sample Invoice

```bash
python -m ai_accountant.cli process sample_invoice.txt
```

You'll see:
```
✓ Parsed: Amazon Web Services - USD 68.19
✓ Journal entry #1 created

Account                          Description              Debit    Credit
─────────────────────────────────────────────────────────────────────────
6100 - Office Supplies          Amazon - INV-2024-001    68.19
2000 - Accounts Payable         Amazon - INV-2024-001             68.19
```

### Step 3: Review and Approve

```bash
python -m ai_accountant.cli review
```

Type `a` to approve! ✅

## 🎓 Learning Path

### Week 1: Getting Started
- [ ] Install and configure system
- [ ] Process sample invoice
- [ ] Review and approve first entry
- [ ] Understand the output

### Week 2: Customization
- [ ] Add your vendor rules
- [ ] Add category rules
- [ ] Process real invoices
- [ ] Review accuracy

### Week 3: Automation
- [ ] Set up file monitoring
- [ ] Use the ./files folder for invoices
- [ ] Test automatic processing
- [ ] Establish review routine

### Week 4: Optimization
- [ ] Fine-tune rules
- [ ] Adjust for your workflow
- [ ] Export data if needed
- [ ] Train team members

## 🔧 System Check

Run this to verify everything is set up correctly:

```bash
python quickstart.py
```

It checks:
- ✓ Python version
- ✓ Virtual environment
- ✓ Dependencies
- ✓ Environment file
- ✓ API keys
- ✓ Tesseract OCR
- ✓ Database

## 🎨 How It Works (Visual)

```
📄 Invoice File
    ↓
👀 Monitor Detects
    ↓
📖 AI Reads & Parses
    ↓
💾 Saves to Database
    ↓
🎯 Applies Rules
    ↓
📝 Creates Journal Entry
    ↓
👤 You Review & Approve
    ↓
✅ Done!
```

## 🌟 Key Features

### 1. Smart Parsing
- Reads PDFs and images
- Understands invoice layouts
- Extracts all key data
- Handles various formats

### 2. Rule-Based Accounting
- Match vendors to accounts
- Match categories to accounts
- Priority-based matching
- Easy to customize

### 3. Double-Entry Bookkeeping
- Proper debit/credit entries
- Balanced entries
- Standard accounting format
- Audit-ready

### 4. Review Workflow
- All entries proposed first
- Interactive approval
- Beautiful terminal UI
- Batch processing

## 🚦 Common Workflows

### Daily Use
```bash
# Morning: Start monitor
python -m ai_accountant.monitor

# Throughout day: Drop invoices in folder
# (They process automatically)

# Evening: Review and approve
python -m ai_accountant.cli review
```

### Manual Processing
```bash
# Process one invoice
python -m ai_accountant.cli process invoice.pdf

# Review it
python -m ai_accountant.cli review
```

### Batch Processing
```bash
# Process multiple invoices
for file in invoices/*.pdf; do
    python -m ai_accountant.cli process "$file"
done

# Review all at once
python -m ai_accountant.cli review
```

## ❓ FAQ

**Q: Do I need both OpenAI and Anthropic?**  
A: No, just one. Choose based on your preference.

**Q: Can it handle scanned invoices?**  
A: Yes! Install Tesseract OCR for image processing.

**Q: What if it makes a mistake?**  
A: You review and approve all entries before they're finalized.

**Q: Can I customize the accounts?**  
A: Yes! Add rules via CLI or edit the database.

**Q: Is my data secure?**  
A: Yes! Everything is stored locally. API calls are encrypted.

**Q: What invoice formats are supported?**  
A: PDF, PNG, JPG, JPEG, TIFF, BMP

**Q: Can I export to QuickBooks?**  
A: Not yet, but you can export to CSV (see EXAMPLES.md)

## 🆘 Troubleshooting

### Problem: "No module named 'ai_accountant'"
**Solution:** Make sure you're in the project directory and virtual environment is activated

### Problem: "API key not configured"
**Solution:** Edit .env file and add your API key

### Problem: "Can't extract text from PDF"
**Solution:** Install Tesseract OCR

### Problem: "Wrong account assigned"
**Solution:** Add a more specific rule with higher priority

**More help:** See SETUP_GUIDE.md troubleshooting section

## 🎯 Next Steps

1. **Right Now:** Run `python quickstart.py` to check your setup
2. **Today:** Process your first real invoice
3. **This Week:** Add rules for your common vendors
4. **This Month:** Set up automatic monitoring

## 📞 Getting Help

1. Check **GETTING_STARTED.md** for setup issues
2. Check **EXAMPLES.md** for usage examples
3. Check **SETUP_GUIDE.md** for detailed troubleshooting
4. Review the sample invoice processing

## 🎉 Success Looks Like...

After setup, your workflow becomes:

**Before AI Accountant:**
1. Open invoice
2. Open accounting software
3. Manually type vendor name
4. Manually type amount
5. Manually select accounts
6. Manually create entry
7. Double-check for errors
8. Repeat for each invoice
⏱️ **Time: 5-10 minutes per invoice**

**After AI Accountant:**
1. Drop invoice in folder
2. Review proposed entry
3. Click approve
⏱️ **Time: 30 seconds per invoice**

**Result: 90% time savings + fewer errors!** 🚀

## ✅ Ready to Start?

Choose your path:

**🏃 Fast Track (5 min):**
```bash
python quickstart.py
python -m ai_accountant.cli process sample_invoice.txt
```

**📚 Complete Setup (15 min):**
Read [GETTING_STARTED.md](GETTING_STARTED.md)

**🎓 Learn Everything:**
Read [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)

---

**Welcome to automated accounting!** 🎊

Questions? Check the documentation files or run `python quickstart.py` to verify your setup.


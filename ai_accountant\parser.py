"""Invoice parsing using AI and OCR"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import PyPDF2

from ai_accountant.config import Config


class InvoiceParser:
    """Parse invoices using OCR and AI"""
    
    def __init__(self, ai_provider: Optional[str] = None):
        self.ai_provider = ai_provider or Config.AI_PROVIDER
        
        if self.ai_provider == "openai":
            from openai import OpenAI
            self.client = OpenAI(api_key=Config.OPENAI_API_KEY)
            self.model = Config.OPENAI_MODEL
        elif self.ai_provider == "anthropic":
            from anthropic import Anthropic
            self.client = Anthropic(api_key=Config.ANTHROPIC_API_KEY)
            self.model = Config.ANTHROPIC_MODEL
        else:
            raise ValueError(f"Unsupported AI provider: {self.ai_provider}")
    
    def parse_invoice(self, file_path: str) -> Dict[str, Any]:
        """
        Parse an invoice file and extract structured data
        
        Args:
            file_path: Path to the invoice file (PDF or image)
            
        Returns:
            Dictionary containing parsed invoice data
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Invoice file not found: {file_path}")
        
        # Extract text from file
        text = self._extract_text(file_path)
        
        if not text.strip():
            raise ValueError(f"No text could be extracted from {file_path}")
        
        # Use AI to parse the text into structured data
        parsed_data = self._parse_with_ai(text)
        
        # Add metadata
        parsed_data["file_path"] = str(file_path)
        parsed_data["file_name"] = file_path.name
        parsed_data["parsed_at"] = datetime.utcnow().isoformat()
        
        # Save as JSON
        self._save_json(parsed_data, file_path)
        
        return parsed_data
    
    def _extract_text(self, file_path: Path) -> str:
        """Extract text from PDF or image file"""
        extension = file_path.suffix.lower()
        
        if extension == ".pdf":
            return self._extract_from_pdf(file_path)
        elif extension in {".png", ".jpg", ".jpeg", ".tiff", ".bmp"}:
            return self._extract_from_image(file_path)
        else:
            raise ValueError(f"Unsupported file type: {extension}")
    
    def _extract_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF"""
        text = ""
        
        # Try to extract text directly from PDF
        try:
            with open(file_path, "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Direct PDF text extraction failed: {e}")
        
        # If no text extracted, use OCR on PDF images
        if not text.strip():
            try:
                images = convert_from_path(file_path)
                for image in images:
                    text += pytesseract.image_to_string(image) + "\n"
            except Exception as e:
                print(f"PDF OCR extraction failed: {e}")
        
        return text
    
    def _extract_from_image(self, file_path: Path) -> str:
        """Extract text from image using OCR"""
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            return text
        except Exception as e:
            raise ValueError(f"Failed to extract text from image: {e}")
    
    def _parse_with_ai(self, text: str) -> Dict[str, Any]:
        """Use AI to parse extracted text into structured data"""
        
        prompt = f"""Parse the following invoice text and extract structured information.
Return a JSON object with the following fields:
- vendor_name: Name of the vendor/supplier
- invoice_number: Invoice number
- invoice_date: Invoice date (YYYY-MM-DD format if possible)
- due_date: Payment due date (YYYY-MM-DD format if possible)
- subtotal: Subtotal amount (number only)
- tax_amount: Tax amount (number only)
- total_amount: Total amount (number only)
- currency: Currency code (e.g., USD, EUR)
- line_items: Array of line items, each with:
  - description: Item description
  - quantity: Quantity (number)
  - unit_price: Unit price (number)
  - amount: Line total (number)
  - category: Suggested category (e.g., "Office Supplies", "Software", "Services")

If any field cannot be determined, use null. Extract all amounts as numbers without currency symbols.

Invoice text:
{text}

Return only valid JSON, no additional text."""

        if self.ai_provider == "openai":
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at parsing invoices and extracting structured data. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            result = response.choices[0].message.content
        else:  # anthropic
            response = self.client.messages.create(
                model=self.model,
                max_tokens=2000,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            result = response.content[0].text
        
        # Parse JSON response
        try:
            # Clean up response if it contains markdown code blocks
            result = result.strip()
            if result.startswith("```json"):
                result = result[7:]
            if result.startswith("```"):
                result = result[3:]
            if result.endswith("```"):
                result = result[:-3]
            result = result.strip()
            
            parsed_data = json.loads(result)
            return parsed_data
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse AI response as JSON: {e}\nResponse: {result}")
    
    def _save_json(self, data: Dict[str, Any], original_file: Path):
        """Save parsed data as JSON"""
        json_filename = original_file.stem + "_parsed.json"
        json_path = Config.JSON_OUTPUT_DIR / json_filename
        
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Saved parsed data to {json_path}")
        
        return json_path


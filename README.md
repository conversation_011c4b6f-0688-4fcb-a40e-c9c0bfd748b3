# AI Accountant

An intelligent accounting assistant that automatically processes invoices and generates journal entries based on customizable rules.

## Features

- 📄 **Invoice Parsing**: Automatically extracts data from PDF and image invoices using AI
- 🤖 **Smart Processing**: Uses OpenAI or Anthropic AI to understand invoice content
- 📊 **Rule-Based Accounting**: Applies customizable rules to generate accurate journal entries
- 👁️ **Desktop Monitoring**: Watches a folder for new invoices and processes them automatically
- ✅ **Review & Approve**: Review proposed journal entries before finalizing
- 💾 **JSON Export**: Saves parsed invoice data in structured JSON format

## Installation

1. **Clone the repository**
   ```bash
   cd ai_accountant
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   # Windows
   venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Tesseract OCR** (for image processing)
   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
   - Linux: `sudo apt-get install tesseract-ocr`
   - Mac: `brew install tesseract`

5. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   # Default invoice folder is ./files (already created)
   ```

6. **Initialize database**
   ```bash
   python -m ai_accountant.init_db
   ```

## Usage

### Start the Invoice Monitor

```bash
python -m ai_accountant.monitor
```

This will watch the `./files` folder for new invoice files.

### Process a Single Invoice

```bash
python -m ai_accountant.cli process path/to/invoice.pdf
```

### Review Pending Journal Entries

```bash
python -m ai_accountant.cli review
```

### Manage Accounting Rules

```bash
# List all rules
python -m ai_accountant.cli rules list

# Add a vendor rule
python -m ai_accountant.cli rules add-vendor "Amazon" --expense-account "6100" --description "Office Supplies"

# Add a category rule
python -m ai_accountant.cli rules add-category "Software" --expense-account "6200" --description "Software Subscriptions"
```

## Project Structure

```
ai_accountant/
├── ai_accountant/
│   ├── __init__.py
│   ├── config.py           # Configuration management
│   ├── models.py           # Database models
│   ├── parser.py           # Invoice parsing logic
│   ├── rules_engine.py     # Accounting rules application
│   ├── journal_entry.py    # Journal entry generation
│   ├── monitor.py          # File monitoring service
│   ├── cli.py              # Command-line interface
│   └── init_db.py          # Database initialization
├── data/                   # Database and processed files
├── tests/                  # Unit tests
├── requirements.txt
├── .env.example
└── README.md
```

## Accounting Rules

The system supports multiple types of rules:

1. **Vendor Rules**: Match specific vendors to accounts
2. **Category Rules**: Match invoice categories/descriptions to accounts
3. **Amount Rules**: Apply different accounts based on amount thresholds
4. **Default Rules**: Fallback rules when no specific match is found

## Example Workflow

1. Save an invoice PDF to the `./files` folder
2. AI Accountant detects the new file
3. Invoice is parsed using AI to extract:
   - Vendor name
   - Invoice date and number
   - Line items with descriptions and amounts
   - Tax amounts
   - Total amount
4. Parsed data is saved as JSON
5. Rules engine matches the invoice to accounting rules
6. Journal entry is generated (debit/credit)
7. You review and approve the entry via CLI
8. Entry is finalized and saved to database

## License

MIT


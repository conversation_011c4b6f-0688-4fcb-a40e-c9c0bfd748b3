"""Command-line interface for AI Accountant"""

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
from pathlib import Path

from ai_accountant.config import Config
from ai_accountant.parser import InvoiceParser
from ai_accountant.journal_entry import JournalEntryGenerator
from ai_accountant.rules_engine import RulesEngine
from ai_accountant.models import get_session

app = typer.Typer(help="AI Accountant - Intelligent Invoice Processing")
console = Console()


@app.command()
def process(
    file_path: str = typer.Argument(..., help="Path to invoice file"),
    auto_approve: bool = typer.Option(False, "--approve", "-a", help="Auto-approve journal entry")
):
    """Process a single invoice file"""
    try:
        Config.validate()
        
        console.print(f"\n[bold blue]Processing invoice:[/bold blue] {file_path}\n")
        
        # Parse invoice
        parser = InvoiceParser()
        invoice_data = parser.parse_invoice(file_path)
        
        vendor = invoice_data.get("vendor_name", "Unknown")
        total = invoice_data.get("total_amount", 0)
        currency = invoice_data.get("currency", "USD")
        
        console.print(f"[green]✓[/green] Parsed: {vendor} - {currency} {total}")
        
        # Generate journal entry
        generator = JournalEntryGenerator()
        journal_entry = generator.process_invoice(invoice_data)
        
        console.print(f"[green]✓[/green] Journal entry #{journal_entry.id} created\n")
        
        # Display entry details
        _display_journal_entry(journal_entry.id)
        
        # Auto-approve if requested
        if auto_approve:
            generator.approve_journal_entry(journal_entry.id)
            console.print("\n[green]✓ Journal entry approved[/green]")
        else:
            console.print("\n[yellow]Use 'cli review' to review and approve this entry[/yellow]")
        
    except Exception as e:
        console.print(f"[red]✗ Error: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def review():
    """Review pending journal entries"""
    try:
        generator = JournalEntryGenerator()
        pending = generator.get_pending_entries()
        
        if not pending:
            console.print("\n[green]No pending journal entries to review[/green]\n")
            return
        
        console.print(f"\n[bold]Found {len(pending)} pending journal entries[/bold]\n")
        
        for entry in pending:
            _display_journal_entry(entry.id)
            
            # Ask for approval
            action = typer.prompt(
                "\nAction? [a]pprove / [r]eject / [s]kip / [q]uit",
                default="s"
            ).lower()
            
            if action == "a":
                generator.approve_journal_entry(entry.id)
                console.print("[green]✓ Approved[/green]\n")
            elif action == "r":
                generator.reject_journal_entry(entry.id)
                console.print("[red]✗ Rejected[/red]\n")
            elif action == "q":
                break
            else:
                console.print("[yellow]Skipped[/yellow]\n")
        
        console.print("[green]Review complete[/green]\n")
        
    except Exception as e:
        console.print(f"[red]✗ Error: {e}[/red]")
        raise typer.Exit(1)


def _display_journal_entry(entry_id: int):
    """Display journal entry details"""
    generator = JournalEntryGenerator()
    details = generator.get_entry_details(entry_id)
    
    if not details:
        console.print(f"[red]Entry #{entry_id} not found[/red]")
        return
    
    # Create header panel
    header = f"[bold]Journal Entry #{details['id']}[/bold]\n"
    header += f"Date: {details['date']}\n"
    header += f"Reference: {details['reference']}\n"
    header += f"Status: {details['status']}"
    
    if details['invoice']:
        header += f"\n\nVendor: {details['invoice']['vendor']}\n"
        header += f"Invoice: {details['invoice']['invoice_number']}\n"
        header += f"Total: {details['invoice']['currency']} {details['invoice']['total']}"
    
    console.print(Panel(header, box=box.ROUNDED))
    
    # Create table for journal lines
    table = Table(show_header=True, header_style="bold magenta", box=box.SIMPLE)
    table.add_column("Account", style="cyan")
    table.add_column("Description")
    table.add_column("Debit", justify="right", style="green")
    table.add_column("Credit", justify="right", style="red")
    
    total_debit = 0
    total_credit = 0
    
    for line in details['lines']:
        table.add_row(
            f"{line['account_code']} - {line['account_name']}",
            line['description'],
            f"{line['debit']:.2f}" if line['debit'] > 0 else "",
            f"{line['credit']:.2f}" if line['credit'] > 0 else ""
        )
        total_debit += line['debit']
        total_credit += line['credit']
    
    table.add_row("", "[bold]TOTAL[/bold]", f"[bold]{total_debit:.2f}[/bold]", f"[bold]{total_credit:.2f}[/bold]")
    
    console.print(table)
    
    # Check if balanced
    if abs(total_debit - total_credit) < 0.01:
        console.print("[green]✓ Entry is balanced[/green]")
    else:
        console.print("[red]✗ Entry is NOT balanced![/red]")


@app.command("rules")
def rules_command():
    """Manage accounting rules"""
    rules_app = typer.Typer(help="Manage accounting rules")
    
    @rules_app.command("list")
    def list_rules():
        """List all accounting rules"""
        engine = RulesEngine()
        rules = engine.list_rules()
        
        if not rules:
            console.print("\n[yellow]No rules defined[/yellow]\n")
            return
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("ID", style="cyan")
        table.add_column("Type")
        table.add_column("Match")
        table.add_column("Debit Account")
        table.add_column("Credit Account")
        table.add_column("Priority", justify="right")
        
        for rule in rules:
            table.add_row(
                str(rule.id),
                rule.rule_type,
                rule.match_value or "",
                f"{rule.debit_account} - {rule.debit_account_name}",
                f"{rule.credit_account} - {rule.credit_account_name}",
                str(rule.priority)
            )
        
        console.print("\n")
        console.print(table)
        console.print("\n")
    
    rules_app()


@app.command("add-vendor-rule")
def add_vendor_rule(
    vendor: str = typer.Argument(..., help="Vendor name to match"),
    expense_account: str = typer.Option(..., "--account", "-a", help="Expense account code"),
    account_name: str = typer.Option(..., "--name", "-n", help="Expense account name"),
    description: str = typer.Option("", "--desc", "-d", help="Rule description")
):
    """Add a vendor-based accounting rule"""
    try:
        engine = RulesEngine()
        rule = engine.add_vendor_rule(vendor, expense_account, account_name, description)
        console.print(f"\n[green]✓ Vendor rule created (ID: {rule.id})[/green]\n")
    except Exception as e:
        console.print(f"[red]✗ Error: {e}[/red]")
        raise typer.Exit(1)


@app.command("add-category-rule")
def add_category_rule(
    category: str = typer.Argument(..., help="Category to match"),
    expense_account: str = typer.Option(..., "--account", "-a", help="Expense account code"),
    account_name: str = typer.Option(..., "--name", "-n", help="Expense account name"),
    description: str = typer.Option("", "--desc", "-d", help="Rule description")
):
    """Add a category-based accounting rule"""
    try:
        engine = RulesEngine()
        rule = engine.add_category_rule(category, expense_account, account_name, description)
        console.print(f"\n[green]✓ Category rule created (ID: {rule.id})[/green]\n")
    except Exception as e:
        console.print(f"[red]✗ Error: {e}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()

